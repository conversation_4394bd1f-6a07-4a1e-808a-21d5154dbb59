I am giving you a log xml file that was generated in retail till. 
log of receipt n is between <rcp no="n-1" ts="..."> and <rcp no="n" ts="...">
<idat> tag contains the data input by the cashier. item number or barcode either manually or automated by barcode scanner. the last rcp can just be below <rcp no="n" ts="..">
<icod> tag contains value of the key pressed(very important) where each code has different meaning. here is the mapping of code and corresponding key:
Cancel                            1
Enter                             2
Scan                              3
Deposit                           4
Withdrawal                        5
Multiplier                        6
Enter Price                       7
Gift Card                         8
Return                            9
PLU Query                        10
Price                            12
Total                            13
Text ID                          14
Till Inventory Query             15
Line Correction                  17
Payment                          18
Park Receipt                     20
Staff                            22
Export                           23
Salesperson Approval             24
Auth                             25
Till Sign In/Out                 26
Till Float Banking               27
Inventory                        28
Requisition Number               29
Customer Number                  30
Shipping                         31
Exchange                         32
Payment Correction               33
Price Correction                 34
Repeat                           35
Manual Percentage                36
Manual Discount                  37
Value Certificate                38
General Query                    39
Change                           40
Cancel Receipt                   41
Reversal                         42
Open Drawer                      43
Till Revenue Query               44
Receipt Correction               46
Membership Number                48
Order Number                     49
Return Tag                       50
Alternative Currency             51
Return Tag 2                     52
IIN                              53
Bonus                            54
Seek                             55
Deposit Account                  56
Prompt for Dimension             57
Exit Assistent Mode              58
Call for Help                    59
New Price                        60
Customer Type                    61
Menu                             62
Tax Refund                       63
Back                             64
Config                           65
External Customer                66
Delete Line                      67
Total Discount                   68
Script                           69
Itemrule Map                     70
KBD                              81
Permission Key                   82
Card Swiped                      83
SYS                              84
Line Select                      85
Panel No Return                  88
BUP                              89
BDWN                             90
BRWS                             91
Up                               92
Down                             93
Panel                            94
Info                             95
Precode                          96
Copy                             97
Function                         98
Journal                          99
Copy 100                        100
DLIN Dummy                      101

<dsp*> tags contain what are being displayed in corresponding display after data input or key press

Now I am giving you an xml log. You should give me the flow of execution line by line in the following format:

receipt no n
 input                 timestamp               output    timestamp

get the timestamps for corresponding input and output. mantain the order of the sequence. what output was logged against what input. follow the format.you should ignore the following tags
<jou>
<lin>
<fk>
<key>
just write the corresponding key name for <icod> tags. and just put the input instead of idat: 2. just 2, not idat: 2
the keep the output in the same cell but mention in the parenthesis which display shows what.