import xml.etree.ElementTree as ET
from datetime import datetime
from tabulate import tabulate

# Key code mapping as provided in prompt2.txt
key_mapping = {
    '1': 'Cancel', '2': 'Enter', '3': 'Scan', '4': 'Deposit', '5': 'Withdrawal',
    '6': 'Multiplier', '7': 'Enter Price', '8': 'Gift Card', '9': 'Return',
    '10': 'PLU Query', '12': 'Price', '13': 'Total', '14': 'Text ID',
    '15': 'Till Inventory Query', '17': 'Line Correction', '18': 'Payment',
    '20': 'Park Receipt', '22': 'Staff', '23': 'Export', '24': 'Salesperson Approval',
    '25': 'Auth', '26': 'Till Sign In/Out', '27': 'Till Float Banking', '28': 'Inventory',
    '29': 'Requisition Number', '30': 'Customer Number', '31': 'Shipping', '32': 'Exchange',
    '33': 'Payment Correction', '34': 'Price Correction', '35': 'Repeat', '36': 'Manual Percentage',
    '37': 'Manual Discount', '38': 'Value Certificate', '39': 'General Query', '40': 'Change',
    '41': 'Cancel Receipt', '42': 'Reversal', '43': 'Open Drawer', '44': 'Till Revenue Query',
    '46': 'Receipt Correction', '48': 'Membership Number', '49': 'Order Number', '50': 'Return Tag',
    '51': 'Alternative Currency', '52': 'Return Tag 2', '53': 'IIN', '54': 'Bonus', '55': 'Seek',
    '56': 'Deposit Account', '57': 'Prompt for Dimension', '58': 'Exit Assistant Mode', '59': 'Call for Help',
    '60': 'New Price', '61': 'Customer Type', '62': 'Menu', '63': 'Tax Refund', '64': 'Back',
    '65': 'Config', '66': 'External Customer', '67': 'Delete Line', '68': 'Total Discount', '69': 'Script',
    '70': 'Itemrule Map', '81': 'KBD', '82': 'Permission Key', '83': 'Card Swiped', '84': 'SYS',
    '85': 'Line Select', '88': 'Panel No Return', '89': 'BUP', '90': 'BDWN', '91': 'BRWS',
    '92': 'Up', '93': 'Down', '94': 'Panel', '95': 'Info', '96': 'Precode', '97': 'Copy',
    '98': 'Function', '99': 'Journal', '100': 'Copy 100', '101': 'DLIN Dummy'
}

def parse_timestamp(ts):
    """Parse timestamp string into datetime object."""
    return datetime.strptime(ts, "%Y-%m-%d %H:%M:%S:%f")

def process_xml_to_text(input_xml, output_txt):
    """Convert XML log file to human-readable text file."""
    # Parse XML file
    try:
        tree = ET.parse(input_xml)
        root = tree.getroot()
        
        # Print root tag and first level children to understand structure
        print(f"Root tag: {root.tag}")
        print("First level children:")
        for child in root:
            print(f"  - {child.tag}")
        
        # Check if any receipts are found
        receipts = root.findall('.//rcp')
        print(f"Found {len(receipts)} receipts in the XML file")
        
        # Try a different approach if no receipts found
        if len(receipts) == 0:
            receipts = root.findall('rcp')  # Try direct children
            print(f"Found {len(receipts)} receipts with direct search")
            
            if len(receipts) == 0 and root.tag == 'rcp':
                # Maybe the root itself is a receipt
                receipts = [root]
                print("Using root as receipt")
        
        with open(output_txt, 'w', encoding='utf-8') as f:
            for rcp in receipts:
                receipt_no = rcp.get('no', 'Unknown')
                f.write(f"Receipt No {receipt_no}\n")

                # Collect events
                events = []

                # Process all children recursively to get events from lin elements
                def process_element(element):
                    for child in element:
                        tag = child.tag
                        # Skip journal and function key tags
                        if tag in ['jou', 'fk']:
                            continue

                        ts = child.get('ts')
                        if ts is None:
                            continue  # Skip if no timestamp

                        try:
                            dt = parse_timestamp(ts)
                        except ValueError:
                            continue  # Skip if timestamp can't be parsed

                        text = child.text.strip() if child.text else ""

                        if tag == 'idat':
                            events.append((dt, 'input', f"Data: {text}"))
                        elif tag == 'icod':
                            key_name = key_mapping.get(text, f"Key {text}")
                            events.append((dt, 'input', f"Key: {key_name}"))
                        elif tag.startswith('dsp'):
                            display_num = tag[3:] if len(tag) > 3 else ""
                            output_value = f"{text} (Display {display_num})" if display_num else text
                            events.append((dt, 'output', output_value))
                        # elif tag == 'key':
                        #     key_no = child.get('no', 'Unknown')
                        #     key_name = key_mapping.get(key_no, f"Key {key_no}")
                        #     events.append((dt, 'input', f"Key Press: {key_name}"))
                        # elif tag == 'iconhide':
                        #     events.append((dt, 'output', f"Hide Icon: {text}"))
                        # elif tag == 'iconshow':
                        #     events.append((dt, 'output', f"Show Icon: {text}"))

                        # Recursively process child elements (like lin elements)
                        if tag == 'lin':
                            process_element(child)

                # Process the receipt
                process_element(rcp)

                # Sort events by timestamp
                events.sort(key=lambda x: x[0])

                # Prepare table data
                table_data = []
                for event in events:
                    dt, event_type, value = event
                    ts_str = dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # Remove last 3 digits for readability
                    if event_type == 'input':
                        table_data.append([value, ts_str, '', ''])
                    elif event_type == 'output':
                        table_data.append(['', '', value, ts_str])

                # Define headers and format table
                headers = ['Input', 'Timestamp', 'Output (Display)', 'Timestamp']

                if table_data:  # Only create table if there's data
                    # Set max column widths to prevent overflow
                    table = tabulate(
                        table_data,
                        headers=headers,
                        tablefmt='grid',
                        maxcolwidths=[30, 25, 40, 25]
                    )
                    f.write(table + '\n\n')
                else:
                    f.write("No events found for this receipt.\n\n")
    except ET.ParseError as e:
        print(f"XML parsing error: {e}")
        print("Check if the XML file is well-formed")

if __name__ == "__main__":
    input_xml = "input.xml"  # Verify this path is correct
    output_txt = "output2.txt"
    
    # Check if file exists
    import os
    if not os.path.exists(input_xml):
        print(f"Error: Input file '{input_xml}' not found")
    else:
        process_xml_to_text(input_xml, output_txt)
