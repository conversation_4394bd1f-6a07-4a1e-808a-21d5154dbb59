ReDoc
Orders
get
Returns the order request given an order id
post
Saves an order to the database. Order id will be generated if not found in the order request
API docs by Redocly
Order API
(v1)
Download OpenAPI specification:
Download
Orders
Returns the order request given an order id
path Parameters
orderId
required
integer
<int32>
Id of the order
Responses
200
Success
Response Schema:
application/xml
header
required
any
additional
any
customer
required
any
collect
required
any
customerregistered
any
coupons
object
details
required
object
get
/orders/v1/orderid/{orderId}
https://api.fiftytwo.com
/orders/v1/orderid/{orderId}
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<
order
>
<
header
store
=
"
123
"
chain
=
"
1
"
orderid
=
"
10
"
/>
<
additional
remark
=
"
Example order
"
channel
=
"
1
"
reference
=
"
example   ref
"
externalcustomerid
=
"
externalid
"
/>
<
customer
phone
=
"
+4510111213
"
mail
=
"
<EMAIL>
"
zipcode
=
"
5555
"
/>
<
collect
chain
=
"
1
"
store
=
"
1
"
date
=
"
2018-01-02
"
from
=
"
10:00:00
"
to
=
"
01:00:00
"
/>
<
customerregistered
id
=
"
1000000925
"
type
=
"
8
"
clublist
=
"
1,2,10
"
/>
<
coupons
>
<
coupon
id
=
"
42
"
isunique
=
"
1
"
barcode
=
"
0000000042
"
discountamount
=
"
2
"
/>
</
coupons
>
<
details
>
<
item
number
=
"
5740500000548
"
quantity
=
"
1
"
description
=
"
NEUTRAL    SÆBE
"
unitprice
=
"
29.95
"
totalprice
=
"
29.95
"
>
<
vat
percent
=
"
25.00
"
amount
=
"
5.99
"
/>
<
potentialdiscountgroups
>
<
discountgroup
>
0000000965
</
discountgroup
>
</
potentialdiscountgroups
>
</
item
>
<
item
number
=
"
5710357000183
"
quantity
=
"
2
"
description
=
"
BLOMME    TOMATER
"
unitprice
=
"
14.95
"
totalprice
=
"
29.90
"
>
<
vat
percent
=
"
25.00
"
amount
=
"
5.98
"
/>
<
discount
discountgroup
=
"
56
"
discounttype
=
"
12
"
amount
=
"
12.50
"
/>
<
potentialdiscountgroups
>
<
discountgroup
>
0000000965
</
discountgroup
>
<
discountgroup
>
0000001200
</
discountgroup
>
</
potentialdiscountgroups
>
</
item
>
<
item
number
=
"
5740700301582
"
description
=
"
GULD TUBORG
"
quantity
=
"
3
"
unitprice
=
"
10.95
"
totalprice
=
"
32.85
"
agerestricted
=
"
18
"
>
<
vat
percent
=
"
25.00
"
amount
=
"
6.57
"
/>
<
linkeditem
number
=
"
7000
"
description
=
"
FLASKE PANT
"
unitprice
=
"
1.50
"
totalprice
=
"
4.50
"
quantity
=
"
2
"
>
<
vat
percent
=
"
25.00
"
amount
=
"
1.12
"
/>
</
linkeditem
>
<
discount
discountgroup
=
"
1680
"
discounttype
=
"
0
"
amount
=
"
2.90
"
/>
</
item
>
<
item
number
=
"
210137
"
description
=
"
ROASTBEEF
"
quantity
=
"
1.5
"
unitprice
=
"
145.00
"
totalprice
=
"
217.50
"
>
<
vat
percent
=
"
25.00
"
amount
=
"
43.50
"
/>
<
potentialdiscountgroups
>
<
discountgroup
>
0000001200
</
discountgroup
>
</
potentialdiscountgroups
>
</
item
>
</
details
>
</
order
>
Saves an order to the database. Order id will be generated if not found in the order request
Request Body schema:
application/xml
Order request as text/xml
header
required
any
additional
any
customer
required
any
collect
required
any
customerregistered
any
coupons
object
coupon
required
any
details
required
object
item
required
object
Responses
200
Success
post
/orders/v1
https://api.fiftytwo.com
/orders/v1
Request samples
Payload
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<
order
>
<
header
store
=
"
123
"
chain
=
"
1
"
orderid
=
"
10
"
/>
<
additional
remark
=
"
Example order
"
channel
=
"
1
"
reference
=
"
example   ref
"
externalcustomerid
=
"
externalid
"
/>
<
customer
phone
=
"
+4510111213
"
mail
=
"
<EMAIL>
"
zipcode
=
"
5555
"
/>
<
collect
chain
=
"
1
"
store
=
"
1
"
date
=
"
2018-01-02
"
from
=
"
10:00:00
"
to
=
"
01:00:00
"
/>
<
customerregistered
id
=
"
1000000925
"
type
=
"
8
"
clublist
=
"
1,2,10
"
/>
<
coupons
>
<
coupon
id
=
"
42
"
isunique
=
"
1
"
barcode
=
"
0000000042
"
discountamount
=
"
2
"
/>
</
coupons
>
<
details
>
<
item
number
=
"
5740500000548
"
quantity
=
"
1
"
description
=
"
NEUTRAL    SÆBE
"
unitprice
=
"
29.95
"
totalprice
=
"
29.95
"
>
<
vat
percent
=
"
25.00
"
amount
=
"
5.99
"
/>
<
potentialdiscountgroups
>
<
discountgroup
>
0000000965
</
discountgroup
>
</
potentialdiscountgroups
>
</
item
>
<
item
number
=
"
5710357000183
"
quantity
=
"
2
"
description
=
"
BLOMME    TOMATER
"
unitprice
=
"
14.95
"
totalprice
=
"
29.90
"
>
<
vat
percent
=
"
25.00
"
amount
=
"
5.98
"
/>
<
discount
discountgroup
=
"
56
"
discounttype
=
"
12
"
amount
=
"
12.50
"
/>
<
potentialdiscountgroups
>
<
discountgroup
>
0000000965
</
discountgroup
>
<
discountgroup
>
0000001200
</
discountgroup
>
</
potentialdiscountgroups
>
</
item
>
<
item
number
=
"
5740700301582
"
description
=
"
GULD TUBORG
"
quantity
=
"
3
"
unitprice
=
"
10.95
"
totalprice
=
"
32.85
"
agerestricted
=
"
18
"
>
<
vat
percent
=
"
25.00
"
amount
=
"
6.57
"
/>
<
linkeditem
number
=
"
7000
"
description
=
"
FLASKE PANT
"
unitprice
=
"
1.50
"
totalprice
=
"
4.50
"
quantity
=
"
2
"
>
<
vat
percent
=
"
25.00
"
amount
=
"
1.12
"
/>
</
linkeditem
>
<
discount
discountgroup
=
"
1680
"
discounttype
=
"
0
"
amount
=
"
2.90
"
/>
</
item
>
<
item
number
=
"
210137
"
description
=
"
ROASTBEEF
"
quantity
=
"
1.5
"
unitprice
=
"
145.00
"
totalprice
=
"
217.50
"
>
<
vat
percent
=
"
25.00
"
amount
=
"
43.50
"
/>
<
potentialdiscountgroups
>
<
discountgroup
>
0000001200
</
discountgroup
>
</
potentialdiscountgroups
>
</
item
>
</
details
>
</
order
>
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8"?>
<
order
>
<
header
orderid
=
"
10
"
/>
</
order
>