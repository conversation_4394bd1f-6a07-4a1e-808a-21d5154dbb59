Auth
API docs by Redocly
Auth<PERSON><PERSON> (v1)
Download OpenAPI specification:Download
Auth
/v1/token
REQUEST BODY SCHEMA: application/json
clientId
required
string non-empty
clientSecret
required
string non-empty
audience
required
string non-empty
Responses
200
Success
POST
/v1/token
Request samples
Payload
Content type
application/json
Copy
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/tokenform
REQUEST BODY SCHEMA: application/x-www-form-urlencoded
ClientId
required
string
ClientSecret
required
string
Audience
required
string
Responses
200
Success
POST
/v1/tokenform
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/user
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
Responses
200
Success
401
Unauthorized
429
Too Many Requests
POST
/v1/user
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string"
}
Response samples
200401429
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/pos
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
POST
/v1/pos
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/store
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
POST
/v1/store
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/refresh
REQUEST BODY SCHEMA:
application/json
text/json
application/*+json
application/json
accessToken
string or null
refreshToken
string or null
Responses
200
Success
POST
/v1/refresh
Request samples
Payload
Content type
application/json
text/json
application/*+json
application/json
Copy
{
"accessToken": "string",
"refreshToken": "string"
}
Auth
API docs by Redocly
AuthAPI (v1)
Download OpenAPI specification:Download
Auth
/v1/token
REQUEST BODY SCHEMA: application/json
clientId
required
string non-empty
clientSecret
required
string non-empty
audience
required
string non-empty
Responses
200
Success
POST
/v1/token
Request samples
Payload
Content type
application/json
Copy
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/tokenform
REQUEST BODY SCHEMA: application/x-www-form-urlencoded
ClientId
required
string
ClientSecret
required
string
Audience
required
string
Responses
200
Success
POST
/v1/tokenform
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/user
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
Responses
200
Success
401
Unauthorized
429
Too Many Requests
POST
/v1/user
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string"
}
Response samples
200401429
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/pos
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
POST
/v1/pos
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/store
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
POST
/v1/store
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/refresh
REQUEST BODY SCHEMA:
application/json
text/json
application/*+json
application/json
accessToken
string or null
refreshToken
string or null
Responses
200
Success
POST
/v1/refresh
Request samples
Payload
Content type
application/json
text/json
application/*+json
application/json
Copy
{
"accessToken": "string",
"refreshToken": "string"
}
Auth
API docs by Redocly
AuthAPI (v1)
Download OpenAPI specification:Download
Auth
/v1/token
REQUEST BODY SCHEMA: application/json
clientId
required
string non-empty
clientSecret
required
string non-empty
audience
required
string non-empty
Responses
200
Success
POST
/v1/token
Request samples
Payload
Content type
application/json
Copy
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/tokenform
REQUEST BODY SCHEMA: application/x-www-form-urlencoded
ClientId
required
string
ClientSecret
required
string
Audience
required
string
Responses
200
Success
POST
/v1/tokenform
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/user
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
Responses
200
Success
401
Unauthorized
429
Too Many Requests
POST
/v1/user
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string"
}
Response samples
200401429
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/pos
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
POST
/v1/pos
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/store
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
POST
/v1/store
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/refresh
REQUEST BODY SCHEMA:
application/json
text/json
application/*+json
application/json
accessToken
string or null
refreshToken
string or null
Responses
200
Success
POST
/v1/refresh
Request samples
Payload
Content type
application/json
text/json
application/*+json
application/json
Copy
{
"accessToken": "string",
"refreshToken": "string"
}
Auth
API docs by Redocly
AuthAPI (v1)
Download OpenAPI specification:Download
Auth
/v1/token
REQUEST BODY SCHEMA: application/json
clientId
required
string non-empty
clientSecret
required
string non-empty
audience
required
string non-empty
Responses
200
Success
POST
/v1/token
Request samples
Payload
Content type
application/json
Copy
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/tokenform
REQUEST BODY SCHEMA: application/x-www-form-urlencoded
ClientId
required
string
ClientSecret
required
string
Audience
required
string
Responses
200
Success
POST
/v1/tokenform
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/user
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
Responses
200
Success
401
Unauthorized
429
Too Many Requests
POST
/v1/user
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string"
}
Response samples
200401429
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/pos
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
POST
/v1/pos
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/store
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
POST
/v1/store
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/refresh
REQUEST BODY SCHEMA:
application/json
text/json
application/*+json
application/json
accessToken
string or null
refreshToken
string or null
Responses
200
Success
POST
/v1/refresh
Request samples
Payload
Content type
application/json
text/json
application/*+json
application/json
Copy
{
"accessToken": "string",
"refreshToken": "string"
}
Auth
API docs by Redocly
Auth
API docs by Redocly
Auth
Auth
Auth
Auth
API docs by Redocly
API docs by Redocly
AuthAPI (v1)
Download OpenAPI specification:Download
Auth
/v1/token
REQUEST BODY SCHEMA: application/json
clientId
required
string non-empty
clientSecret
required
string non-empty
audience
required
string non-empty
Responses
200
Success
POST
/v1/token
Request samples
Payload
Content type
application/json
Copy
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/tokenform
REQUEST BODY SCHEMA: application/x-www-form-urlencoded
ClientId
required
string
ClientSecret
required
string
Audience
required
string
Responses
200
Success
POST
/v1/tokenform
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/user
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
Responses
200
Success
401
Unauthorized
429
Too Many Requests
POST
/v1/user
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string"
}
Response samples
200401429
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/pos
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
POST
/v1/pos
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/store
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
POST
/v1/store
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/refresh
REQUEST BODY SCHEMA:
application/json
text/json
application/*+json
application/json
accessToken
string or null
refreshToken
string or null
Responses
200
Success
POST
/v1/refresh
Request samples
Payload
Content type
application/json
text/json
application/*+json
application/json
Copy
{
"accessToken": "string",
"refreshToken": "string"
}
AuthAPI (v1)
Download OpenAPI specification:Download
AuthAPI (v1)
Download OpenAPI specification:Download
AuthAPI (v1)
Download OpenAPI specification:Download
AuthAPI (v1)
(v1)
Download OpenAPI specification:Download
Download
Auth
Auth
Auth
Auth
/v1/token
REQUEST BODY SCHEMA: application/json
clientId
required
string non-empty
clientSecret
required
string non-empty
audience
required
string non-empty
Responses
200
Success
POST
/v1/token
Request samples
Payload
Content type
application/json
Copy
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/token
REQUEST BODY SCHEMA: application/json
clientId
required
string non-empty
clientSecret
required
string non-empty
audience
required
string non-empty
Responses
200
Success
POST
/v1/token
Request samples
Payload
Content type
application/json
Copy
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/token
REQUEST BODY SCHEMA: application/json
clientId
required
string non-empty
clientSecret
required
string non-empty
audience
required
string non-empty
Responses
200
Success
/v1/token
REQUEST BODY SCHEMA: application/json
application/json
clientId
required
string non-empty
clientSecret
required
string non-empty
audience
required
string non-empty
clientId
required
string non-empty
clientSecret
required
string non-empty
audience
required
string non-empty
clientId
required
string non-empty
clientId
required
clientId
required
string non-empty
string non-empty
string non-empty
string
non-empty
non-empty
clientSecret
required
string non-empty
clientSecret
required
clientSecret
required
string non-empty
string non-empty
string non-empty
string
non-empty
non-empty
audience
required
string non-empty
audience
required
audience
required
string non-empty
string non-empty
string non-empty
string
non-empty
non-empty
Responses
200
Success
Responses
200
Success
200
Success
200
Success
Success
POST
/v1/token
Request samples
Payload
Content type
application/json
Copy
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
POST
/v1/token
POST
/v1/token
POST
/v1/token
Request samples
Payload
Content type
application/json
Copy
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
Request samples
Payload
Content type
application/json
Copy
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
Payload
Payload
Content type
application/json
Copy
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
Content type
application/json
Copy
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
Content type
application/json
Content type
application/json
Copy
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
Copy
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
Copy
Copy
Copy
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
}
{
"clientId": "string",
"clientSecret": "string",
"audience": "string"
"clientId": "string",
"clientId": "string",
"clientId"
"string"
,
"clientSecret": "string",
"clientSecret": "string",
"clientSecret"
"string"
,
"audience": "string"
"audience": "string"
"audience"
"string"
}
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
200
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Content type
application/json
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Copy
Copy
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
"access_token": "string",
"access_token": "string",
"access_token"
"string"
,
"refresh_token": "string",
"refresh_token": "string",
"refresh_token"
"string"
,
"expires_in": 0,
"expires_in": 0,
"expires_in"
0
,
"token_type": "string"
"token_type": "string"
"token_type"
"string"
}
/v1/tokenform
REQUEST BODY SCHEMA: application/x-www-form-urlencoded
ClientId
required
string
ClientSecret
required
string
Audience
required
string
Responses
200
Success
POST
/v1/tokenform
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/tokenform
REQUEST BODY SCHEMA: application/x-www-form-urlencoded
ClientId
required
string
ClientSecret
required
string
Audience
required
string
Responses
200
Success
POST
/v1/tokenform
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/tokenform
REQUEST BODY SCHEMA: application/x-www-form-urlencoded
ClientId
required
string
ClientSecret
required
string
Audience
required
string
Responses
200
Success
/v1/tokenform
REQUEST BODY SCHEMA: application/x-www-form-urlencoded
application/x-www-form-urlencoded
ClientId
required
string
ClientSecret
required
string
Audience
required
string
ClientId
required
string
ClientSecret
required
string
Audience
required
string
ClientId
required
string
ClientId
required
ClientId
required
string
string
string
string
ClientSecret
required
string
ClientSecret
required
ClientSecret
required
string
string
string
string
Audience
required
string
Audience
required
Audience
required
string
string
string
string
Responses
200
Success
Responses
200
Success
200
Success
200
Success
Success
POST
/v1/tokenform
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
POST
/v1/tokenform
POST
/v1/tokenform
POST
/v1/tokenform
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Response samples
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
200
200
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Content type
application/json
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Copy
Copy
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
"access_token": "string",
"access_token": "string",
"access_token"
"string"
,
"refresh_token": "string",
"refresh_token": "string",
"refresh_token"
"string"
,
"expires_in": 0,
"expires_in": 0,
"expires_in"
0
,
"token_type": "string"
"token_type": "string"
"token_type"
"string"
}
/v1/user
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
Responses
200
Success
401
Unauthorized
429
Too Many Requests
POST
/v1/user
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string"
}
Response samples
200401429
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/user
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
Responses
200
Success
401
Unauthorized
429
Too Many Requests
POST
/v1/user
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string"
}
Response samples
200401429
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/user
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
Responses
200
Success
401
Unauthorized
429
Too Many Requests
/v1/user
REQUEST BODY SCHEMA: application/json
application/json
username
required
string non-empty
password
required
string non-empty
username
required
string non-empty
password
required
string non-empty
username
required
string non-empty
username
required
username
required
string non-empty
string non-empty
string non-empty
string
non-empty
non-empty
password
required
string non-empty
password
required
password
required
string non-empty
string non-empty
string non-empty
string
non-empty
non-empty
Responses
200
Success
401
Unauthorized
429
Too Many Requests
Responses
200
Success
200
Success
200
Success
Success
401
Unauthorized
401
Unauthorized
401
Unauthorized
Unauthorized
429
Too Many Requests
429
Too Many Requests
429
Too Many Requests
Too Many Requests
POST
/v1/user
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string"
}
Response samples
200401429
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
POST
/v1/user
POST
/v1/user
POST
/v1/user
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string"
}
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string"
}
Payload
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string"
}
Content type
application/json
Copy
{
"username": "string",
"password": "string"
}
Content type
application/json
Content type
application/json
Copy
{
"username": "string",
"password": "string"
}
Copy
{
"username": "string",
"password": "string"
}
Copy
Copy
Copy
{
"username": "string",
"password": "string"
}
{
"username": "string",
"password": "string"
}
{
"username": "string",
"password": "string"
}
{
"username": "string",
"password": "string"
"username": "string",
"username": "string",
"username"
"string"
,
"password": "string"
"password": "string"
"password"
"string"
}
Response samples
200401429
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Response samples
200401429
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
200401429
200
401
429
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Content type
application/json
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Copy
Copy
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
"access_token": "string",
"access_token": "string",
"access_token"
"string"
,
"refresh_token": "string",
"refresh_token": "string",
"refresh_token"
"string"
,
"expires_in": 0,
"expires_in": 0,
"expires_in"
0
,
"token_type": "string"
"token_type": "string"
"token_type"
"string"
}
/v1/pos
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
POST
/v1/pos
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/pos
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
POST
/v1/pos
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/pos
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
/v1/pos
REQUEST BODY SCHEMA: application/json
application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
username
required
string non-empty
username
required
username
required
string non-empty
string non-empty
string non-empty
string
non-empty
non-empty
password
required
string non-empty
password
required
password
required
string non-empty
string non-empty
string non-empty
string
non-empty
non-empty
activationKey
required
string non-empty
activationKey
required
activationKey
required
string non-empty
string non-empty
string non-empty
string
non-empty
non-empty
amount
integer <int32>
amount
amount
integer <int32>
integer <int32>
integer <int32>
integer
<int32>
Responses
200
Success
401
Unauthorized
Responses
200
Success
200
Success
200
Success
Success
401
Unauthorized
401
Unauthorized
401
Unauthorized
Unauthorized
POST
/v1/pos
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
POST
/v1/pos
POST
/v1/pos
POST
/v1/pos
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Payload
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Content type
application/json
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Copy
Copy
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
"username": "string",
"username": "string",
"username"
"string"
,
"password": "string",
"password": "string",
"password"
"string"
,
"activationKey": "string",
"activationKey": "string",
"activationKey"
"string"
,
"amount": 0
"amount": 0
"amount"
0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
200401
200
401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Content type
application/json
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Copy
Copy
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
"access_token": "string",
"access_token": "string",
"access_token"
"string"
,
"refresh_token": "string",
"refresh_token": "string",
"refresh_token"
"string"
,
"expires_in": 0,
"expires_in": 0,
"expires_in"
0
,
"token_type": "string"
"token_type": "string"
"token_type"
"string"
}
/v1/store
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
POST
/v1/store
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/store
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
POST
/v1/store
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
/v1/store
REQUEST BODY SCHEMA: application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
Responses
200
Success
401
Unauthorized
/v1/store
REQUEST BODY SCHEMA: application/json
application/json
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
username
required
string non-empty
password
required
string non-empty
activationKey
required
string non-empty
amount
integer <int32>
username
required
string non-empty
username
required
username
required
string non-empty
string non-empty
string non-empty
string
non-empty
non-empty
password
required
string non-empty
password
required
password
required
string non-empty
string non-empty
string non-empty
string
non-empty
non-empty
activationKey
required
string non-empty
activationKey
required
activationKey
required
string non-empty
string non-empty
string non-empty
string
non-empty
non-empty
amount
integer <int32>
amount
amount
integer <int32>
integer <int32>
integer <int32>
integer
<int32>
Responses
200
Success
401
Unauthorized
Responses
200
Success
200
Success
200
Success
Success
401
Unauthorized
401
Unauthorized
401
Unauthorized
Unauthorized
POST
/v1/store
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
POST
/v1/store
POST
/v1/store
POST
/v1/store
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Request samples
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Payload
Payload
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Content type
application/json
Content type
application/json
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
Copy
Copy
Copy
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
}
{
"username": "string",
"password": "string",
"activationKey": "string",
"amount": 0
"username": "string",
"username": "string",
"username"
"string"
,
"password": "string",
"password": "string",
"password"
"string"
,
"activationKey": "string",
"activationKey": "string",
"activationKey"
"string"
,
"amount": 0
"amount": 0
"amount"
0
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Response samples
200401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
200401
200
401
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Content type
application/json
Content type
application/json
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
Copy
Copy
Copy
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
}
{
"access_token": "string",
"refresh_token": "string",
"expires_in": 0,
"token_type": "string"
"access_token": "string",
"access_token": "string",
"access_token"
"string"
,
"refresh_token": "string",
"refresh_token": "string",
"refresh_token"
"string"
,
"expires_in": 0,
"expires_in": 0,
"expires_in"
0
,
"token_type": "string"
"token_type": "string"
"token_type"
"string"
}
/v1/refresh
REQUEST BODY SCHEMA:
application/json
text/json
application/*+json
application/json
accessToken
string or null
refreshToken
string or null
Responses
200
Success
POST
/v1/refresh
Request samples
Payload
Content type
application/json
text/json
application/*+json
application/json
Copy
{
"accessToken": "string",
"refreshToken": "string"
}
/v1/refresh
REQUEST BODY SCHEMA:
application/json
text/json
application/*+json
application/json
accessToken
string or null
refreshToken
string or null
Responses
200
Success
POST
/v1/refresh
Request samples
Payload
Content type
application/json
text/json
application/*+json
application/json
Copy
{
"accessToken": "string",
"refreshToken": "string"
}
/v1/refresh
REQUEST BODY SCHEMA:
application/json
text/json
application/*+json
application/json
accessToken
string or null
refreshToken
string or null
Responses
200
Success
/v1/refresh
REQUEST BODY SCHEMA:
application/json
text/json
application/*+json
application/json
application/json
text/json
application/*+json
application/json
application/json
text/json
application/*+json
application/json
text/json
application/*+json
application/json
accessToken
string or null
refreshToken
string or null
accessToken
string or null
refreshToken
string or null
accessToken
string or null
accessToken
accessToken
string or null
string or null
string or null
string or null
refreshToken
string or null
refreshToken
refreshToken
string or null
string or null
string or null
string or null
Responses
200
Success
Responses
200
Success
200
Success
200
Success
Success
POST
/v1/refresh
Request samples
Payload
Content type
application/json
text/json
application/*+json
application/json
Copy
{
"accessToken": "string",
"refreshToken": "string"
}
POST
/v1/refresh
POST
/v1/refresh
POST
/v1/refresh
Request samples
Payload
Content type
application/json
text/json
application/*+json
application/json
Copy
{
"accessToken": "string",
"refreshToken": "string"
}
Request samples
Payload
Content type
application/json
text/json
application/*+json
application/json
Copy
{
"accessToken": "string",
"refreshToken": "string"
}
Payload
Payload
Content type
application/json
text/json
application/*+json
application/json
Copy
{
"accessToken": "string",
"refreshToken": "string"
}
Content type
application/json
text/json
application/*+json
application/json
Copy
{
"accessToken": "string",
"refreshToken": "string"
}
Content type
application/json
text/json
application/*+json
application/json
Content type
application/json
text/json
application/*+json
application/json
application/json
text/json
application/*+json
application/json
text/json
application/*+json
application/json
Copy
{
"accessToken": "string",
"refreshToken": "string"
}
Copy
{
"accessToken": "string",
"refreshToken": "string"
}
Copy
Copy
Copy
{
"accessToken": "string",
"refreshToken": "string"
}
{
"accessToken": "string",
"refreshToken": "string"
}
{
"accessToken": "string",
"refreshToken": "string"
}
{
"accessToken": "string",
"refreshToken": "string"
"accessToken": "string",
"accessToken": "string",
"accessToken"
"string"
,
"refreshToken": "string"
"refreshToken": "string"
"refreshToken"
"string"
}
