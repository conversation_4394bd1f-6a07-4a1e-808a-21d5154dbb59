{"Masterdata API": {"title": "52ViKING master data API (v1)", "endpoints": [], "sections": [{"level": "h1", "text": "52ViKING master data API (v1)"}, {"level": "h2", "text": "ArticleFamilies"}, {"level": "h2", "text": "Get discount family by ID, chain, and store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Get discount family by ID and chain"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of discount families for specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of discount families for all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete discount family from specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete discount family from all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "ArticleGroups"}, {"level": "h2", "text": "Get article group by ID and chain"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Get article group by ID, chain, and store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of article groups for specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of article groups for all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete article group from specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete article group from all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "ArticleLists"}, {"level": "h2", "text": "Get article list by ID, chain, and store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Get article list by ID and chain"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of article lists for specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of article lists for all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete article list from specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete article list from all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Articles"}, {"level": "h2", "text": "Get article by ID, chain, and store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Get article by ID and chain"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of articles for specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of articles for all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete article from specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete article from all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Campaigns"}, {"level": "h2", "text": "Get campaign by ID, chain, and store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Get campaign by ID and chain"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of campaigns for specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of campaigns for all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete campaign from specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete campaign from all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Customers"}, {"level": "h2", "text": "Get customer by ID, chain, and store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Get customer by ID and chain"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of customers for specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of customers for all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete customer from specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete customer from all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Departments"}, {"level": "h2", "text": "Get department by ID, chain, and store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Get department by ID and chain"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of departments for specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of departments for all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete departments from specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete departments from all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "DiscountGroups"}, {"level": "h2", "text": "Get discount group by ID, chain, and store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Get discount group by ID and chain"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of discount groups for specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of discount groups for all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete discount groups from specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete discount group from all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "SalesGroups"}, {"level": "h2", "text": "Get sales group by ID, chain, and store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Get sales group by ID and chain"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of sales groups for specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of sales groups for all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete sales group from specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete sales group from all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "StoreGroups"}, {"level": "h2", "text": "Get store group by ID, chain, and store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Get store group by ID and chain"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update a list of store groups on all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete a store group from all stores"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "BaseData"}, {"level": "h2", "text": "Get processing status for master data."}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Stores"}, {"level": "h2", "text": "Get store by ID and chain"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Get store by ID and chain  Deprecated"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update specific store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Insert/update list of stores on 52ViKING enterprise service"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Delete store from 52ViKING enterprise service"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}], "full_text": "\n    ArticleFamiliesgetGet discount family by ID, chain, and storegetGet discount family by ID and chainpostInsert/update list of discount families for specific storepostInsert/update list of discount families for all storesdelDelete discount family from specific storedelDelete discount family from all storesArticleGroupsgetGet article group by ID and chaingetGet article group by ID, chain, and storepostInsert/update list of article groups for specific storepostInsert/update list of article groups for all storesdelDelete article group from specific storedelDelete article group from all storesArticleListsgetGet article list by ID, chain, and storegetGet article list by ID and chainpostInsert/update list of article lists for specific storepostInsert/update list of article lists for all storesdelDelete article list from specific storedelDelete article list from all storesArticlesgetGet article by ID, chain, and storegetGet article by ID and chainpostInsert/update list of articles for specific storepostInsert/update list of articles for all storesdelDelete article from specific storedelDelete article from all storesCampaignsgetGet campaign by ID, chain, and storegetGet campaign by ID and chainpostInsert/update list of campaigns for specific storepostInsert/update list of campaigns for all storesdelDelete campaign from specific storedelDelete campaign from all storesCustomersgetGet customer by ID, chain, and storegetGet customer by ID and chainpostInsert/update list of customers for specific storepostInsert/update list of customers for all storesdelDelete customer from specific storedelDelete customer from all storesDepartmentsgetGet department by ID, chain, and storegetGet department by ID and chainpostInsert/update list of departments for specific storepostInsert/update list of departments for all storesdelDelete departments from specific storedelDelete departments from all storesDiscountGroupsgetGet discount group by ID, chain, and storegetGet discount group by ID and chainpostInsert/update list of discount groups for specific storepostInsert/update list of discount groups for all storesdelDelete discount groups from specific storedelDelete discount group from all storesSalesGroupsgetGet sales group by ID, chain, and storegetGet sales group by ID and chainpostInsert/update list of sales groups for specific storepostInsert/update list of sales groups for all storesdelDelete sales group from specific storedelDelete sales group from all storesStoreGroupsgetGet store group by ID, chain, and storegetGet store group by ID and chainpostInsert/update a list of store groups on all storesdelDelete a store group from all storesBaseDatagetGet processing status for master data.StoresgetGet store by ID and chaingetGet store by ID and chainpostInsert/update specific storepostInsert/update list of stores on 52ViKING enterprise servicedelDelete store from 52ViKING enterprise serviceAPI docs by Redocly52ViKING master data API (v1)Download OpenAPI specification:Download52ViKING: <EMAIL>   The Master data API is an asynchronous API where all POST methods return 202 accepted and supply a reference to the created resource in the location header.With that reference, you can query the resource for an updated status as a result of the backend process of the resource.There are two different sets of POST methods:If you only specify chain, distribution to stores is processed by the 52ViKING enterprise controller (EC). Master data will be identical for all stores (with the exception of article data).If you specify both chain and store, data is store-specific and handled directly by individual stores.If you're in doubt about which POST method set to use, speak with your Fiftytwo consultant.Schema validation of content is done synchronously.Note that all POST endpoints return 413 Payload Too Large if the body content exceeds 2 MB.Field descriptions in the schemas may contain references to Bpar.fieldname. Bpar is a store configuration utility that's managed by Fiftytwo consultants in cooperation with your organization.ArticleFamilies\n  \n    \n                        A discount family (previously known as an article family) ties related articles together with the puspose of applying a discount.\n                    \n    \n                        With a discount family you can apply a discount across different article numbers that your organization has tied together with a family number. There's no limit to the number of articles that can be in the same discount family, but one article can only belong in one discount family. Articles in the same discount family don't need to have the same sales price.\n                    \n    \n                        With a discount family you can also create discount ladders (also known as discount chains) with discounts/prices based on purchases of different quantities. Example: Two for EUR 20, three for EUR 29, five for EUR 40.\n                    \n  \nGet discount family by ID, chain, and store path", "tables_count": 87, "code_blocks_count": 43}, "Receipt API": {"title": "Receipt API (v1)", "endpoints": [], "sections": [{"level": "h1", "text": "Receipt API (v1)"}, {"level": "h2", "text": "EndOfDay reports from Fiftytwo"}, {"level": "h2", "text": "Retrieve an End Of Day report by specifying (ChainNo, StoreNo and SessionNo).\r\nAn End Of Day report contains aggregated information for a salesday which\r\nis identified by a sessionid."}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "ReceiptManagement"}, {"level": "h2", "text": "/receiptmanagement/v{version}"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h2", "text": "Receipts from Fiftytwo"}, {"level": "h2", "text": "Retrieve list of receipts for store identified by (ChainNo, StoreNo)."}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Retrieve list of receipts for a chain identified by ChainNo)."}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Receipts to Fiftytwo"}, {"level": "h2", "text": "Retrieve status of receipt based on specified parameters"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Retrieve status of receipt in REST-like format based on specified parameters"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Retrieve status of receipt based on order ID"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Save receipt as XML in payload"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}], "full_text": "\n    EndOfDay reports from FiftytwogetRetrieve an End Of Day report by specifying (ChainNo, StoreNo and SessionNo).\r\nAn End Of Day report contains aggregated information for a salesday which\r\nis identified by a sessionid.ReceiptManagementpost/receiptmanagement/v{version}Receipts from FiftytwogetRetrieve list of receipts for store identified by (ChainNo, StoreNo).getRetrieve list of receipts for a chain identified by ChainNo).Receipts to FiftytwogetRetrieve status of receipt based on specified parametersgetRetrieve status of receipt in REST-like format based on specified parametersgetRetrieve status of receipt based on order IDpostSave receipt as XML in payloadAPI docs by RedoclyReceipt API (v1)Download OpenAPI specification:Download52RETAIL: <EMAIL>   The Receipt API is used to post receipts from external systems like webshops.This enables customers to return goods in a physical store.Posting receipts to Fiftytwo enables one point of entry for receipts e.g using method belowget receipts produced by Fiftytwo (or posted via method above).get end of day reports produced by FiftytwoEndOfDay reports from FiftytwoRetrieve an End Of Day report by specifying (ChainNo, StoreNo and SessionNo).\r\nAn End Of Day report contains aggregated information for a salesday which\r\nis identified by a sessionid. The report contains\nturnover per articlegroupPayments per paymenttypePayments per cardtypeAccount paymentsTotal Flow in - amount added to tillsTotal Flow out - amount removed from tillsBank transfers - local and foreign currencySystem Discrepancy - difference between sales and paymentsDrawer discrepancy - expected vs. actual amount in drawerpath ParameterschainNorequiredinteger <int32>  The ChainNo\nstoreNorequiredinteger <int32>  The StoreNo\nsessionNorequiredinteger <int32>  Method returns a list of reports starting from the sessionNo+1. The client should keep the last received sessionNo.\n            If no reports are found an empty array is returned.\nversionrequiredstring query Parameterslimitinteger <int32>  Default:  1 Return up to limit end of day reports.\nResponses200 Success\nget/endofday/v{version}/chain/{chainNo}/store/{storeNo}/session/{sessionNo}https://api.fiftytwo.com/endofday/v{version}/chain/{chainNo}/store/{storeNo}/session/{sessionNo} Response samples 200Content typeapplication/xmlCopy<EndOfDayReports xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n\t<EndOfDayReport>\n\t\t<ChainNo>0</ChainNo>\n\t\t<StoreNo>2203</StoreNo>\n\t\t<SessionNo>2242</SessionNo>\n\t\t<SessionDate>2021-05-30T00:00:00</SessionDate>\n\t\t<Report>\n\t\t\t<AccountingReport>\n\t\t\t\t<ArticleGroupTurnover>\n\t\t\t\t\t<ArticleGroup id=\"000000\" financeAccount=\"\" amount=\"110750.17\" VATcode=\"1\" VATPercent=\"25\" descripttion=\"\" />\n\t\t\t\t\t<ArticleGroup id=\"000020\" financeAccount=\"\" amount=\"1164\" VATcode=\"0\" VATPercent=\"0\" descripttion=\"EMBALLAGE\" />\n\t\t\t\t\t<ArticleGroup id=\"000030\" financeAccount=\"\" amount=\"4147.5\" VATcode=\"0\" VATPercent=\"0\" descripttion=\"DELI\" />\n\t\t\t\t\t<ArticleGroup id=\"000040\" financeAccount=\"\" amount=\"17796\" VATcode=\"0\" VATPercent=\"0\" descripttion=\"FRUGT OG GRØNT\" />\n\t\t\t\t\t<ArticleGroup id=\"000060\" financeAccount=\"\" amount=\"7493.01\" VATcode=\"0\" VATPercent=\"0\" descripttion=\"BAGER\" />\n\t\t\t\t\t<ArticleGroup id=\"000080\" financeAccount=\"\" amount=\"49087.6\" VATcode=\"0\" VATPercent=\"0\" descripttion=\"KOLONIAL\" />\n\t\t\t\t\t<ArticleGroup id=\"000083\" financeAccount=\"\" amount=\"14807.45\" VATcode=\"0\" VATPercent=\"0\" descripttion=\"KIOSK\" />\n\t\t\t\t\t<ArticleGroup id=\"000090\" financeAccount=\"\" amount=\"36241.21\" VATcode=\"0\" VATPercent=\"0\" descripttion=\"SLAGTER\" />\n\t\t\t\t\t<ArticleGroup id=\"000310\" financeAccount=\"\" amount=\"966\" VATcode=\"0\" VATPercent=\"0\" descripttion=\"TEXTIL\" />\n\t\t\t\t\t<ArticleGroup id=\"000320\" financeAccount=\"\" amount=\"568\" VATcode=\"1\" VATPercent=\"25\" descripttion=\"PLANTER\" />\n\t\t\t\t\t<ArticleGroup id=\"000321\" financeAccount=\"\" amount=\"66\" VATcode=\"1\" VATPercent=\"25\" descripttion=\"HUS &amp; HAVE\" />\n\t\t\t\t\t<ArticleGroup id=\"000330\" financeAccount=\"\" amount=\"1716\" VATcode=\"1\" VATPercent=\"25\" descripttion=\"BOLIG &amp; FRITID\" />\n\t\t\t\t\t<ArticleGroup id=\"000370\" financeAccount=\"\" amount=\"16405.23\" VATcode=\"0\" VATPercent=\"0\" descripttion=\"DIVERSE\" />\n\t\t\t\t\t<ArticleGroup id=\"000390\" financeAccount=\"\" amount=\"1722.2\" VATcode=\"1\" VATPercent=\"25\" descripttion=\"ELEKTRONIK\" />\n\t\t\t\t\t<ArticleGroup id=\"000901\" financeAccount=\"\" amount=\"-7197.5\" VATcode=\"0\" VATPercent=\"0\" descripttion=\"ANDET SALG\" />\n\t\t\t\t</ArticleGroupTurnover>\n\t\t\t\t<TotalArticleTurnover financeAccount=\"111111\" amount=\"255732.87\" />\n\t\t\t\t<Services />\n\t\t\t\t<TotalServices financeAccount=\"131313\" amount=\"0\" />\n\t\t\t\t<GrandTotal financeAccount=\"141414\" amount=\"255732.87\" />\n\t\t\t\t<PaymentTypes>\n\t\t\t\t\t<PaymentType id=\"000\" financeAccount=\"000000\" amount=\"0\" description=\"KONTANT\" />\n\t\t\t\t\t<PaymentType id=\"041\" financeAccount=\"000000\" amount=\"26317.5\" description=\"KONTANT CMS\" />\n\t\t\t\t\t<PaymentType id=\"219\" financeAccount=\"000000\" amount=\"226429.41\" description=\"DANKORT\" />\n\t\t\t\t\t<CardPayments", "tables_count": 13, "code_blocks_count": 4}, "Order API": {"title": "Order API (v1)", "endpoints": [], "sections": [{"level": "h1", "text": "Order API (v1)"}, {"level": "h2", "text": "Orders"}, {"level": "h2", "text": "Returns the order request given an order id"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "Saves an order to the database. Order id will be generated if not found in the order request"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}], "full_text": "\n    OrdersgetReturns the order request given an order idpostSaves an order to the database. Order id will be generated if not found in the order requestAPI docs by RedoclyOrder API (v1)Download OpenAPI specification:DownloadOrdersReturns the order request given an order id path ParametersorderIdrequiredinteger <int32>  Id of the order\nResponses200 Success\nget/orders/v1/orderid/{orderId}https://api.fiftytwo.com/orders/v1/orderid/{orderId} Response samples 200Content typeapplication/xmlCopy<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n<order>\n\t<header store=\"123\" chain=\"1\" orderid =\"10\"/>\n\t<additional remark=\"Example order \" channel=\"1\" reference=\"example   ref \" externalcustomerid=\"externalid\"/>\n\t<customer phone=\"+4510111213\" mail=\"<EMAIL>\" zipcode=\"5555\"/>\n\t<collect chain=\"1\" store=\"1\" date=\"2018-01-02\" from=\"10:00:00\" to=\"01:00:00\"/>\n\t<customerregistered id=\"1000000925\" type=\"8\" clublist=\"1,2,10\"/>\n\t<coupons>\n\t\t<coupon id=\"42\" isunique=\"1\" barcode=\"0000000042\" discountamount=\"2\"/>\n\t</coupons>\n\t<details>\n\t\t<item number=\"5740500000548\" quantity=\"1\" description=\"NEUTRAL    SÆBE \" unitprice=\"29.95\" totalprice=\"29.95\">\n\t\t\t<vat percent=\"25.00\" amount=\"5.99\"/>\n\t\t\t<potentialdiscountgroups>\n\t\t\t\t<discountgroup>0000000965</discountgroup>\n\t\t\t</potentialdiscountgroups>\n\t\t</item>\n\t\t<item number=\"5710357000183\" quantity=\"2\" description=\"BLOMME    TOMATER \" unitprice=\"14.95\" totalprice=\"29.90\">\n\t\t\t<vat percent=\"25.00\" amount=\"5.98\"/>\n\t\t\t<discount discountgroup=\"56\" discounttype=\"12\" amount=\"12.50\"/>\n\t\t\t<potentialdiscountgroups>\n\t\t\t\t<discountgroup>0000000965</discountgroup>\n\t\t\t\t<discountgroup>0000001200</discountgroup>\n\t\t\t</potentialdiscountgroups>\n\t\t</item>\n\t\t<item number=\"5740700301582\" description=\"GULD TUBORG \" quantity=\"3\" unitprice=\"10.95\" totalprice=\"32.85\" agerestricted=\"18\">\n\t\t\t<vat percent=\"25.00\" amount=\"6.57\"/>\n\t\t\t<linkeditem number=\"7000\" description=\"FLASKE PANT \" unitprice=\"1.50\" totalprice=\"4.50\" quantity=\"2\">\n\t\t\t\t<vat percent=\"25.00\" amount=\"1.12\"/>\n\t\t\t</linkeditem>\n\t\t\t<discount discountgroup=\"1680\" discounttype=\"0\" amount=\"2.90\"/>\n\t\t</item>\n\t\t<item number=\"210137\" description=\"ROASTBEEF\" quantity=\"1.5\" unitprice=\"145.00\" totalprice=\"217.50\">\n\t\t\t<vat percent=\"25.00\" amount=\"43.50\"/>\n\t\t\t<potentialdiscountgroups>\n\t\t\t\t<discountgroup>0000001200</discountgroup>\n\t\t\t</potentialdiscountgroups>\n\t\t</item>\n\t</details>\n</order>Saves an order to the database. Order id will be generated if not found in the order request Request Body schema: application/xmlOrder request as text/xml\nheaderrequiredany additionalany customerrequiredany collectrequiredany customerregisteredany couponsobject detailsrequiredobject Responses200 Success\npost/orders/v1https://api.fiftytwo.com/orders/v1 Request samples PayloadContent typeapplication/xmlCopy<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n<order>\n\t<header store=\"123\" chain=\"1\" orderid =\"10\"/>\n\t<additional remark=\"Example order \" channel=\"1\" reference=\"example   ref \" externalcustomerid=\"externalid\"/>\n\t<customer phone=\"+4510111213\" mail=\"<EMAIL>\" zipcode=\"5555\"/>\n\t<collect chain=\"1\" store=\"1\" date=\"2018-01-02\" from=\"10:00:00\" to=\"01:00:00\"/>\n\t<customerregistered id=\"1000000925\" type=\"8\" clublist=\"1,2,10\"/>\n\t<coupons>\n\t\t<coupon id=\"42\" isunique=\"1\" barcode=\"0000000042\" discountamount=\"2\"/>\n\t</coupons>\n\t<details>\n\t\t<item number=\"5740500000548\" quantity=\"1\" description=\"NEUTRAL    SÆBE \" unitprice=\"29.95\" totalprice=\"29.95\">\n\t\t\t<vat percent=\"25.00\" amount=\"5.99\"/>\n\t\t\t<potentialdiscountgroups>\n\t\t\t\t<discountgroup>0000000965</discountgroup>\n\t\t\t</potentialdiscountgroups>\n\t\t</item>\n\t\t<item number=\"5710357000183\" quantity=\"2\" description=\"BLOMME    TOMATER \" unitprice=\"14.95\" totalprice=\"29.90\">\n\t\t\t<vat percent=\"25.00\" amount=\"5.98\"/>\n\t\t\t<discount discountgroup=\"56\" discounttype=\"12\" amount=\"12.50\"/>\n\t\t\t<potentialdiscountgroups>\n\t\t\t\t<discountgroup>0000000965</discountgroup>\n\t\t\t\t<discountgroup>0000001200</discountgroup>\n\t\t\t</potentialdiscountgroups>\n\t\t</item>\n\t\t<item number=\"5740700301582\" description=\"GULD TUBORG \" quantity=\"3\" unitprice=\"10.95\" totalprice=\"32.85\" agerestricted=\"18\">\n\t\t\t<vat percent=\"25.00\" amount=\"6.57\"/>\n\t\t\t<linkeditem number=\"7000\" description=\"FLASKE PANT \" unitprice=\"1.50\" totalprice=\"4.50\" quantity=\"2\">\n\t\t\t\t<vat percent=\"25.00\" amount=\"1.12\"/>\n\t\t\t</linkeditem>\n\t\t\t<discount discountgroup=\"1680\" discounttype=\"0\" amount=\"2.90\"/>\n\t\t</item>\n\t\t<item number=\"210137\" description=\"ROASTBEEF\" quantity=\"1.5\" unitprice=\"145.00\" totalprice=\"217.50\">\n\t\t\t<vat percent=\"25.00\" amount=\"43.50\"/>\n\t\t\t<potentialdiscountgroups>\n\t\t\t\t<discountgroup>0000001200</discountgroup>\n\t\t\t</potentialdiscountgroups>\n\t\t</item>\n\t</details>\n</order> Response samples 200Content typeapplication/xmlCopy<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<order>\n\t<header orderid=\"10\"/>\n</order>\n    \n  \n\n", "tables_count": 2, "code_blocks_count": 3}, "Basket API": {"title": "Basket API (v1)", "endpoints": [], "sections": [{"level": "h1", "text": "Basket API (v1)"}, {"level": "h2", "text": "Basket"}, {"level": "h2", "text": "Calcuates the price of the products in a customer's basket."}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}], "full_text": "\n    BasketpostCalcuates the price of the products in a customer's basket.API docs by RedoclyBasket API (v1)Download OpenAPI specification:Download52RETAIL: <EMAIL>   The Basket API is used to:  \n\nCalculate the price for the products in a customer's basket.  \nTo be used from webshops and other sales applications.\n\nBasketCalcuates the price of the products in a customer's basket. path ParametersparamChainrequiredinteger <int32>  Fetch basket response of a chain for a customer.\nparamStorerequiredinteger <int32>  Fetch basket response of a store for a chain.\nRequest Body schema: application/json Any of objectbasketobject (basketinput_basket)  Responses200 OK\npost/basket/calculatebasket/{paramChain}/{paramStore}https://api.fiftytwo.com/basket/calculatebasket/{paramChain}/{paramStore} Request samples PayloadContent typeapplication/jsonCopy Expand all  Collapse all {\"basket\": {\"details\": {\"item\": [{\"number\": 9991123,\"quantity\": 10},{\"number\": 10,\"quantity\": 10},{\"number\": 11,\"quantity\": 10},{\"number\": 12,\"quantity\": 10},{\"number\": 25,\"quantity\": 10}]}}} Response samples 200Content typeapplication/jsonCopy Expand all  Collapse all {\"basket\": {\"summary\": {\"vat\": [{\"percent\": 0,\"amount\": 0,\"vatgroup\": 9}],\"discount\": [{\"discountgroup\": 0,\"family\": 0,\"discounttype\": 0,\"amount\": 0,\"couponid\": 0,\"offerid\": 0,\"campaignid\": 0}],\"service\": [{\"discountgroup\": 0,\"amount\": 0,\"offerid\": 0,\"serviceid\": 0}],\"unclaimedgifts\": {\"unclaimedgift\": [{\"discountgroup\": 0,\"giftlistid\": 0,\"numberofunclaimedgifts\": 9999,\"giftsdeserved\": 0,\"giftquantitysold\": 0}]},\"potentialdiscounts\": {\"potentialdiscount\": [{\"discountgroup\": 0,\"missingquantity\": 0,\"missingamount\": 0,\"missingcondition\": 0}]},\"staffdiscounts\": {\"staffdiscount\": [{\"group\": 9,\"percent\": 0,\"baseamount\": 0,\"amount\": 0}]},\"chain\": 99,\"store\": 99999,\"numberofitems\": 9999,\"subtotal\": 0},\"customerregistered\": {\"id\": 999999999999999,\"type\": 9,\"subtype\": 9,\"clublist\": \"string\"},\"coupons\": {\"coupon\": [{\"id\": 999,\"isunique\": 1,\"value\": 0,\"discountamount\": 0}]},\"details\": {\"item\": [{\"vat\": {\"percent\": 0,\"amount\": 0,\"vatgroup\": 9},\"discount\": [{\"discountgroup\": 0,\"family\": 0,\"discounttype\": 0,\"amount\": 0,\"couponid\": 0,\"offerid\": 0,\"campaignid\": 0}],\"potentialdiscountgroups\": {\"discountgroup\": [0]},\"linkeditem\": [{ }],\"number\": 9999999999999,\"barcode\": 9999999999999,\"referrerArticleId\": 9999999999999,\"packagecount\": -999,\"quantity\": 0,\"totalprice\": 0,\"description\": \"string\",\"unitprice\": 0,\"agerestricted\": 99,\"netunitprice\": 0,\"nettotalprice\": 0}]},\"errors\": {\"itemerror\": [{\"number\": 9999999999999,\"description\": \"string\",\"type\": 9}]}}}\n    \n  \n\n", "tables_count": 2, "code_blocks_count": 2}, "Auth API": {"title": "AuthAPI (v1)", "endpoints": [], "sections": [{"level": "h1", "text": "AuthAPI (v1)"}, {"level": "h2", "text": "<PERSON><PERSON>"}, {"level": "h2", "text": "/v1/token"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "/v1/tokenform"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "/v1/user"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "/v1/pos"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "/v1/store"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}, {"level": "h3", "text": "Response samples"}, {"level": "h2", "text": "/v1/refresh"}, {"level": "h3", "text": "Responses"}, {"level": "h3", "text": "Request samples"}], "full_text": "\n    Authpost/v1/tokenpost/v1/tokenformpost/v1/userpost/v1/pospost/v1/storepost/v1/refreshAPI docs by RedoclyAuthAPI (v1)Download OpenAPI specification:DownloadAuth/v1/token Request Body schema: application/jsonclientIdrequiredstring  non-empty  clientSecretrequiredstring  non-empty  audiencerequiredstring  non-empty  Responses200 Success\npost/v1/tokenhttps://api.fiftytwo.com/v1/token Request samples PayloadContent typeapplication/jsonCopy{\"clientId\": \"string\",\"clientSecret\": \"string\",\"audience\": \"string\"} Response samples 200Content typeapplication/jsonCopy{\"access_token\": \"string\",\"refresh_token\": \"string\",\"expires_in\": 0,\"token_type\": \"string\"}/v1/tokenform Request Body schema: application/x-www-form-urlencodedClientIdrequiredstring ClientSecretrequiredstring Audiencerequiredstring Responses200 Success\npost/v1/tokenformhttps://api.fiftytwo.com/v1/tokenform Response samples 200Content typeapplication/jsonCopy{\"access_token\": \"string\",\"refresh_token\": \"string\",\"expires_in\": 0,\"token_type\": \"string\"}/v1/user Request Body schema: application/jsonusernamerequiredstring  non-empty  passwordrequiredstring  non-empty  Responses200 Success\n401 Unauthorized\n429 Too Many Requests\npost/v1/userhttps://api.fiftytwo.com/v1/user Request samples PayloadContent typeapplication/jsonCopy{\"username\": \"string\",\"password\": \"string\"} Response samples 200401429Content typeapplication/jsonCopy{\"access_token\": \"string\",\"refresh_token\": \"string\",\"expires_in\": 0,\"token_type\": \"string\"}/v1/pos Request Body schema: application/jsonusernamerequiredstring  non-empty  passwordrequiredstring  non-empty  activationKeyrequiredstring  non-empty  amountinteger <int32>  Responses200 Success\n401 Unauthorized\npost/v1/poshttps://api.fiftytwo.com/v1/pos Request samples PayloadContent typeapplication/jsonCopy{\"username\": \"string\",\"password\": \"string\",\"activationKey\": \"string\",\"amount\": 0} Response samples 200401Content typeapplication/jsonCopy{\"access_token\": \"string\",\"refresh_token\": \"string\",\"expires_in\": 0,\"token_type\": \"string\"}/v1/store Request Body schema: application/jsonusernamerequiredstring  non-empty  passwordrequiredstring  non-empty  activationKeyrequiredstring  non-empty  amountinteger <int32>  Responses200 Success\n401 Unauthorized\npost/v1/storehttps://api.fiftytwo.com/v1/store Request samples PayloadContent typeapplication/jsonCopy{\"username\": \"string\",\"password\": \"string\",\"activationKey\": \"string\",\"amount\": 0} Response samples 200401Content typeapplication/jsonCopy{\"access_token\": \"string\",\"refresh_token\": \"string\",\"expires_in\": 0,\"token_type\": \"string\"}/v1/refresh Request Body schema: application/jsontext/jsonapplication/*+jsonapplication/jsonaccessTokenstring or null refreshTokenstring or null Responses200 Success\npost/v1/refreshhttps://api.fiftytwo.com/v1/refresh Request samples PayloadContent typeapplication/jsontext/jsonapplication/*+jsonapplication/jsonCopy{\"accessToken\": \"string\",\"refreshToken\": \"string\"}\n    \n  \n\n", "tables_count": 6, "code_blocks_count": 10}}