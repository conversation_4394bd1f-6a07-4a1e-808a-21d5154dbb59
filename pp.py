import re
import json

def extract_and_format_json(text):
    # Regex pattern to match JSON-like blocks (between curly braces)
    json_blocks = re.findall(r'\{\s*[^{}]*?\}', text, re.DOTALL)

    formatted_jsons = []
    for block in json_blocks:
        # Fix common formatting issues
        fixed = re.sub(r'\s*:\s*', ': ', block)  # normalize colon spacing
        fixed = re.sub(r',\s*\n', ',\n', fixed)  # clean up trailing comma + line spacing
        fixed = re.sub(r'\n+', '\n', fixed)      # remove extra line breaks

        # Try to load and dump to validate JSON (and prettify)
        try:
            obj = json.loads(fixed)
            pretty = json.dumps(obj, indent=4)
            formatted_jsons.append(pretty)
        except Exception as e:
            print(f"Could not parse block:\n{block}\nError: {e}")
            formatted_jsons.append(block)  # fallback: append raw if failed

    return formatted_jsons

# Load your documentation as text
with open("masterdata_api_documentation.txt", "r", encoding="utf-8") as f:
    doc_text = f.read()

# Extract and format
json_outputs = extract_and_format_json(doc_text)

# Save or print formatted results
for i, json_block in enumerate(json_outputs):
    print(f"\n---- JSON Block {i+1} ----")
    print(json_block)
