ReDoc
Basket
post
Calcuates the price of the products in a customer's basket.
API docs by Redocly
Basket API
(v1)
Download OpenAPI specification:
Download
52RETAIL:
<EMAIL>
The Basket API is used to:
Calculate the price for the products in a customer's basket.
To be used from webshops and other sales applications.
Basket
Calcuates the price of the products in a customer's basket.
path Parameters
paramChain
required
integer
<int32>
Fetch basket response of a chain for a customer.
paramStore
required
integer
<int32>
Fetch basket response of a store for a chain.
Request Body schema:
application/json
Any of
object
basket
object
(basketinput_basket)
details
required
object
(basketinput_basket.details)
customerregistered
object
(basketinput_basket.customerregistered)
discountgroupstotrigger
object
(basketinput_basket.discountgroupstotrigger)
offeridstotrigger
object
(basketinput_basket.offeridstotrigger)
discounts
object
(basketinput_basket.discounts)
coupons
object
(basketinput_basket.coupons)
disablediscounts
integer
[ 0 .. 1 ]
Responses
200
OK
Response Schema:
application/json
Any of
object
basket
object
(basketoutput_basket)
post
/basket/calculatebasket/{paramChain}/{paramStore}
https://api.fiftytwo.com
/basket/calculatebasket/{paramChain}/{paramStore}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"basket"
:
{
"details"
:
{
"item"
:
[
{
"number"
:
9991123
,
"quantity"
:
10
}
,
{
"number"
:
10
,
"quantity"
:
10
}
,
{
"number"
:
11
,
"quantity"
:
10
}
,
{
"number"
:
12
,
"quantity"
:
10
}
,
{
"number"
:
25
,
"quantity"
:
10
}
]
}
}
}
Response samples
200
Content type
application/json
Copy
Expand all
Collapse all
{
"basket"
:
{
"summary"
:
{
"vat"
:
[
{
"percent"
:
0
,
"amount"
:
0
,
"vatgroup"
:
9
}
]
,
"discount"
:
[
{
"discountgroup"
:
0
,
"family"
:
0
,
"discounttype"
:
0
,
"amount"
:
0
,
"couponid"
:
0
,
"offerid"
:
0
,
"campaignid"
:
0
}
]
,
"service"
:
[
{
"discountgroup"
:
0
,
"amount"
:
0
,
"offerid"
:
0
,
"serviceid"
:
0
}
]
,
"unclaimedgifts"
:
{
"unclaimedgift"
:
[
{
"discountgroup"
:
0
,
"giftlistid"
:
0
,
"numberofunclaimedgifts"
:
9999
,
"giftsdeserved"
:
0
,
"giftquantitysold"
:
0
}
]
}
,
"potentialdiscounts"
:
{
"potentialdiscount"
:
[
{
"discountgroup"
:
0
,
"missingquantity"
:
0
,
"missingamount"
:
0
,
"missingcondition"
:
0
}
]
}
,
"staffdiscounts"
:
{
"staffdiscount"
:
[
{
"group"
:
9
,
"percent"
:
0
,
"baseamount"
:
0
,
"amount"
:
0
}
]
}
,
"chain"
:
99
,
"store"
:
99999
,
"numberofitems"
:
9999
,
"subtotal"
:
0
}
,
"customerregistered"
:
{
"id"
:
999999999999999
,
"type"
:
9
,
"subtype"
:
9
,
"clublist"
:
"string"
}
,
"coupons"
:
{
"coupon"
:
[
{
"id"
:
999
,
"isunique"
:
1
,
"value"
:
0
,
"discountamount"
:
0
}
]
}
,
"details"
:
{
"item"
:
[
{
"vat"
:
{
"percent"
:
0
,
"amount"
:
0
,
"vatgroup"
:
9
}
,
"discount"
:
[
{
"discountgroup"
:
0
,
"family"
:
0
,
"discounttype"
:
0
,
"amount"
:
0
,
"couponid"
:
0
,
"offerid"
:
0
,
"campaignid"
:
0
}
]
,
"potentialdiscountgroups"
:
{
"discountgroup"
:
[
0
]
}
,
"linkeditem"
:
[
{ }
]
,
"number"
:
9999999999999
,
"barcode"
:
9999999999999
,
"referrerArticleId"
:
9999999999999
,
"packagecount"
:
-999
,
"quantity"
:
0
,
"totalprice"
:
0
,
"description"
:
"string"
,
"unitprice"
:
0
,
"agerestricted"
:
99
,
"netunitprice"
:
0
,
"nettotalprice"
:
0
}
]
}
,
"errors"
:
{
"itemerror"
:
[
{
"number"
:
9999999999999
,
"description"
:
"string"
,
"type"
:
9
}
]
}
}
}