ReDoc
ArticleFamilies
get
Get discount family by ID, chain, and store
get
Get discount family by ID and chain
post
Insert/update list of discount families for specific store
post
Insert/update list of discount families for all stores
del
Delete discount family from specific store
del
Delete discount family from all stores
ArticleGroups
get
Get article group by ID and chain
get
Get article group by ID, chain, and store
post
Insert/update list of article groups for specific store
post
Insert/update list of article groups for all stores
del
Delete article group from specific store
del
Delete article group from all stores
ArticleLists
get
Get article list by ID, chain, and store
get
Get article list by ID and chain
post
Insert/update list of article lists for specific store
post
Insert/update list of article lists for all stores
del
Delete article list from specific store
del
Delete article list from all stores
Articles
get
Get article by ID, chain, and store
get
Get article by ID and chain
post
Insert/update list of articles for specific store
post
Insert/update list of articles for all stores
del
Delete article from specific store
del
Delete article from all stores
Campaigns
get
Get campaign by ID, chain, and store
get
Get campaign by ID and chain
post
Insert/update list of campaigns for specific store
post
Insert/update list of campaigns for all stores
del
Delete campaign from specific store
del
Delete campaign from all stores
Customers
get
Get customer by ID, chain, and store
get
Get customer by ID and chain
post
Insert/update list of customers for specific store
post
Insert/update list of customers for all stores
del
Delete customer from specific store
del
Delete customer from all stores
Departments
get
Get department by ID, chain, and store
get
Get department by ID and chain
post
Insert/update list of departments for specific store
post
Insert/update list of departments for all stores
del
Delete departments from specific store
del
Delete departments from all stores
DiscountGroups
get
Get discount group by ID, chain, and store
get
Get discount group by ID and chain
post
Insert/update list of discount groups for specific store
post
Insert/update list of discount groups for all stores
del
Delete discount groups from specific store
del
Delete discount group from all stores
SalesGroups
get
Get sales group by ID, chain, and store
get
Get sales group by ID and chain
post
Insert/update list of sales groups for specific store
post
Insert/update list of sales groups for all stores
del
Delete sales group from specific store
del
Delete sales group from all stores
StoreGroups
get
Get store group by ID, chain, and store
get
Get store group by ID and chain
post
Insert/update a list of store groups on all stores
del
Delete a store group from all stores
BaseData
get
Get processing status for master data.
Stores
get
Get store by ID and chain
get
Get store by ID and chain
post
Insert/update specific store
post
Insert/update list of stores on 52ViKING enterprise service
del
Delete store from 52ViKING enterprise service
API docs by Redocly
52ViKING master data API
(v1)
Download OpenAPI specification:
Download
52ViKING:
<EMAIL>
The Master data API is an asynchronous API where all POST methods return 202 accepted and supply a reference to the created resource in the location header.
With that reference, you can query the resource for an updated status as a result of the backend process of the resource.
There are two different sets of POST methods:
If you only specify chain, distribution to stores is processed by the 52ViKING enterprise controller (EC). Master data will be identical for all stores (with the exception of article data).
If you specify both chain and store, data is store-specific and handled directly by individual stores.
If you're in doubt about which POST method set to use, speak with your Fiftytwo consultant.
Schema validation of content is done synchronously.
Note that all POST endpoints return 413 Payload Too Large if the body content exceeds 2 MB.
Field descriptions in the schemas may contain references to Bpar.fieldname. Bpar is a store configuration utility that's managed by Fiftytwo consultants in cooperation with your organization.
ArticleFamilies
A discount family (previously known as an article family) ties related articles together with the puspose of applying a discount.
With a discount family you can apply a discount across different article numbers that your organization has tied together with a family number. There's no limit to the number of articles that can be in the same discount family, but one article can only belong in one discount family. Articles in the same discount family don't need to have the same sales price.
With a discount family you can also create discount ladders (also known as discount chains) with discounts/prices based on purchases of different quantities. Example: Two for EUR 20, three for EUR 29, five for EUR 40.
Get discount family by ID, chain, and store
path Parameters
articleFamilyId
required
integer
<int64>
The ID of the discount family to retrieve
chainId
required
integer
<int32>
The ID of the chain
storeId
required
integer
<int32>
The ID of the store
Responses
200
Success
Response Schema:
application/json
articleFamilies
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/articlefamilies/v1/chain/{chainId}/store/{storeId}/{articleFamilyId}
https://api.fiftytwo.com
/articlefamilies/v1/chain/{chainId}/store/{storeId}/{articleFamilyId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"articleFamilies"
:
[
{
"articleFamilyId"
:
123
,
"description"
:
"articlefamily 123"
,
"familyDiscount"
:
[
{
"quantity"
:
1
,
"amount"
:
16
}
,
{
"quantity"
:
2
,
"amount"
:
8
}
,
{
"quantity"
:
4
,
"amount"
:
4
}
,
{
"quantity"
:
8
,
"amount"
:
2
}
,
{
"quantity"
:
16
,
"amount"
:
1
}
]
,
"discountInSets"
:
1
,
"discountMaximum"
:
16
,
"clubId"
:
1
,
"receiptTextId"
:
99
,
"receiptGroup"
:
12
}
]
}
Get discount family by ID and chain
path Parameters
articleFamilyId
required
integer
<int64>
The ID of the discount family to retrieve
chainId
required
integer
<int32>
The ID of the chain
Responses
200
Success
Response Schema:
application/json
articleFamilies
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/articlefamilies/v1/chain/{chainId}/{articleFamilyId}
https://api.fiftytwo.com
/articlefamilies/v1/chain/{chainId}/{articleFamilyId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"articleFamilies"
:
[
{
"articleFamilyId"
:
123
,
"description"
:
"articlefamily 123"
,
"familyDiscount"
:
[
{
"quantity"
:
1
,
"amount"
:
16
}
,
{
"quantity"
:
2
,
"amount"
:
8
}
,
{
"quantity"
:
4
,
"amount"
:
4
}
,
{
"quantity"
:
8
,
"amount"
:
2
}
,
{
"quantity"
:
16
,
"amount"
:
1
}
]
,
"discountInSets"
:
1
,
"discountMaximum"
:
16
,
"clubId"
:
1
,
"receiptTextId"
:
99
,
"receiptGroup"
:
12
}
]
}
Insert/update list of discount families for specific store
Not supported when master data is distributed by enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
Request Body schema:
application/json
articleFamilies
required
Array of
objects
Array
articleFamilyId
required
integer
[ 0 .. ******** ]
The ID of the discount family.
description
string
A text that describes the discount family.
familyDiscount
Array of
objects
[ 1 .. 5 ] items
Definition of the discount family's discount ladder (that can have from one to five steps).
discountCode
integer
[ 0 .. 3 ]
Defines the meaning of the amount field in the familyDiscount ladder.
0 = Amount is the discount given when selling the required quantity of articles. Example: Example: Buy three and save EUR 5.00.
1 = Amount is the total price for the required quantity of articles. Example: Buy three for EUR 20.00.
2 = Amount is the discount percentage given when buying the required quantity of articles. Example: Buy three and save 10%.
3 = Amount is the percentage to pay for the cheapest article. Example: Buy three and pay 30% for the cheapest (so 0 means get the cheapest article for free).
discountInSets
integer
Enum:
0
1
Defines if the discount should only be triggered each time the required quantity of articles is reached.
0 = Minimum buy, discount is triggered for all articles after required quantity is reached.
1 = Set-wise, discount is triggered for multiplums of the required quantity
Example where required quantity is three and customer buys four articles:
discountInSets = 0, discount is given for all four articles.
discountInSets = 1, discount is only given for three articles.
discountMaximum
integer
[ 0 .. 999 ]
The maximum number of articles in the discount family that can be sold on one receipt at the discounted price. When the maximum is exceeded, additional articles are sold with no discount.
clubId
integer
[ 0 .. 99 ]
Defines if discount only applies to members of the club with the defined ID. If 0, the discount applies to everyone, regardless of club membership.
receiptTextId
integer
[ 0 .. 999 ]
If you require a special receipt discount text for use with the discount family, you can specify a reference to the required text. The text must be present in the 52ViKING text segments table with the name RTX_yyy, where yyy is the value of the field. Ask your Fiftytwo consultant if you're in doubt.
receiptGroup
integer
[ 0 .. 99 ]
Print group reference for sorting receipts. Print groups are maintained by Fiftytwo consultants.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/articlefamilies/v1/chain/{chain}/store/{store}
https://api.fiftytwo.com
/articlefamilies/v1/chain/{chain}/store/{store}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"articleFamilies"
:
[
{
"articleFamilyId"
:
123
,
"description"
:
"articlefamily 123"
,
"familyDiscount"
:
[
{
"quantity"
:
1
,
"amount"
:
16
}
,
{
"quantity"
:
2
,
"amount"
:
8
}
,
{
"quantity"
:
4
,
"amount"
:
4
}
,
{
"quantity"
:
8
,
"amount"
:
2
}
,
{
"quantity"
:
16
,
"amount"
:
1
}
]
,
"discountInSets"
:
1
,
"discountMaximum"
:
16
,
"clubId"
:
1
,
"receiptTextId"
:
99
,
"receiptGroup"
:
12
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Insert/update list of discount families for all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
Request Body schema:
application/json
articleFamilies
required
Array of
objects
Array
articleFamilyId
required
integer
[ 0 .. ******** ]
The ID of the discount family.
description
string
A text that describes the discount family.
familyDiscount
Array of
objects
[ 1 .. 5 ] items
Definition of the discount family's discount ladder (that can have from one to five steps).
discountCode
integer
[ 0 .. 3 ]
Defines the meaning of the amount field in the familyDiscount ladder.
0 = Amount is the discount given when selling the required quantity of articles. Example: Example: Buy three and save EUR 5.00.
1 = Amount is the total price for the required quantity of articles. Example: Buy three for EUR 20.00.
2 = Amount is the discount percentage given when buying the required quantity of articles. Example: Buy three and save 10%.
3 = Amount is the percentage to pay for the cheapest article. Example: Buy three and pay 30% for the cheapest (so 0 means get the cheapest article for free).
discountInSets
integer
Enum:
0
1
Defines if the discount should only be triggered each time the required quantity of articles is reached.
0 = Minimum buy, discount is triggered for all articles after required quantity is reached.
1 = Set-wise, discount is triggered for multiplums of the required quantity
Example where required quantity is three and customer buys four articles:
discountInSets = 0, discount is given for all four articles.
discountInSets = 1, discount is only given for three articles.
discountMaximum
integer
[ 0 .. 999 ]
The maximum number of articles in the discount family that can be sold on one receipt at the discounted price. When the maximum is exceeded, additional articles are sold with no discount.
clubId
integer
[ 0 .. 99 ]
Defines if discount only applies to members of the club with the defined ID. If 0, the discount applies to everyone, regardless of club membership.
receiptTextId
integer
[ 0 .. 999 ]
If you require a special receipt discount text for use with the discount family, you can specify a reference to the required text. The text must be present in the 52ViKING text segments table with the name RTX_yyy, where yyy is the value of the field. Ask your Fiftytwo consultant if you're in doubt.
receiptGroup
integer
[ 0 .. 99 ]
Print group reference for sorting receipts. Print groups are maintained by Fiftytwo consultants.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/articlefamilies/v1/chain/{chain}
https://api.fiftytwo.com
/articlefamilies/v1/chain/{chain}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"articleFamilies"
:
[
{
"articleFamilyId"
:
123
,
"description"
:
"articlefamily 123"
,
"familyDiscount"
:
[
{
"quantity"
:
1
,
"amount"
:
16
}
,
{
"quantity"
:
2
,
"amount"
:
8
}
,
{
"quantity"
:
4
,
"amount"
:
4
}
,
{
"quantity"
:
8
,
"amount"
:
2
}
,
{
"quantity"
:
16
,
"amount"
:
1
}
]
,
"discountInSets"
:
1
,
"discountMaximum"
:
16
,
"clubId"
:
1
,
"receiptTextId"
:
99
,
"receiptGroup"
:
12
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete discount family from specific store
Not supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
familyId
required
integer
<int64>
The ID of the discount family
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/articlefamilies/v1/chain/{chain}/store/{store}/{familyId}
https://api.fiftytwo.com
/articlefamilies/v1/chain/{chain}/store/{store}/{familyId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete discount family from all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
familyId
required
integer
<int64>
The ID of the discount family
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/articlefamilies/v1/chain/{chain}/{familyId}
https://api.fiftytwo.com
/articlefamilies/v1/chain/{chain}/{familyId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
ArticleGroups
Article groups are part of the article hierarchy chain-store-department-articlegroup-article. Article groups can provide a useful grouping mechanism..
You can use article groups for many purposes, such as article group-based discounts, article group-based statistics, or article group-based thresholds (for example how many articles from an article group that shop assistants can sell with a POS's multiply (X) key).
Get article group by ID and chain
path Parameters
articleGroupId
required
integer
<int64>
The ID of the article group to retrieve
chainId
required
integer
<int32>
The ID of the chain
Responses
200
Success
Response Schema:
application/json
articleGroups
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/articlegroups/v1/chain/{chainId}/{articleGroupId}
https://api.fiftytwo.com
/articlegroups/v1/chain/{chainId}/{articleGroupId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"articleGroups"
:
[
{
"articleGroupId"
:
10
,
"description"
:
"Article Group 10"
,
"department"
:
"10"
,
"discountPercentage"
:
15
,
"minimumPrice"
:
10
,
"maximumPrice"
:
20
,
"account"
:
"**********"
,
"omitFromTurnover"
:
1
,
"statistics"
:
1
,
"joinId"
:
10
,
"receiptGroup"
:
1
,
"specialShelfLabel"
:
0
,
"grossProfitLimit"
:
22.5
,
"maximumPayout"
:
110
}
,
{
"articleGroupId"
:
11
,
"description"
:
"Article Group 11"
,
"department"
:
"11"
,
"discountPercentage"
:
20
,
"minimumPrice"
:
100
,
"maximumPrice"
:
200
,
"account"
:
"**********"
,
"omitFromTurnover"
:
0
,
"statistics"
:
0
,
"joinId"
:
11
,
"receiptGroup"
:
2
,
"specialShelfLabel"
:
0
,
"grossProfitLimit"
:
20
,
"maximumPayout"
:
111
}
]
}
Get article group by ID, chain, and store
path Parameters
articleGroupId
required
integer
<int64>
The ID of the article group to retrieve
chainId
required
integer
<int32>
The ID of the chain
storeId
required
integer
<int32>
The ID of the store
Responses
200
Success
Response Schema:
application/json
articleGroups
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/articlegroups/v1/chain/{chainId}/store/{storeId}/articlegroup/{articleGroupId}
https://api.fiftytwo.com
/articlegroups/v1/chain/{chainId}/store/{storeId}/articlegroup/{articleGroupId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"articleGroups"
:
[
{
"articleGroupId"
:
10
,
"description"
:
"Article Group 10"
,
"department"
:
"10"
,
"discountPercentage"
:
15
,
"minimumPrice"
:
10
,
"maximumPrice"
:
20
,
"account"
:
"**********"
,
"omitFromTurnover"
:
1
,
"statistics"
:
1
,
"joinId"
:
10
,
"receiptGroup"
:
1
,
"specialShelfLabel"
:
0
,
"grossProfitLimit"
:
22.5
,
"maximumPayout"
:
110
}
,
{
"articleGroupId"
:
11
,
"description"
:
"Article Group 11"
,
"department"
:
"11"
,
"discountPercentage"
:
20
,
"minimumPrice"
:
100
,
"maximumPrice"
:
200
,
"account"
:
"**********"
,
"omitFromTurnover"
:
0
,
"statistics"
:
0
,
"joinId"
:
11
,
"receiptGroup"
:
2
,
"specialShelfLabel"
:
0
,
"grossProfitLimit"
:
20
,
"maximumPayout"
:
111
}
]
}
Insert/update list of article groups for specific store
Not supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
Request Body schema:
application/json
articleGroups
required
Array of
objects
Array
articleGroupId
required
integer
[ 1 .. 999999 ]
ID of the article group.
description
string
[ 0 .. 30 ] characters
A short text that describes the article group.
department
string
[ 1 .. 999999 ]
The department that the article group in question belongs to.
discountPercentage
integer
[ 0 .. 10000 ]
Discount percentage that applies to all articles in the article group, where discountPercentage is -1. For more information, see the articles endpoint.
minimumPrice
integer
[ 0 .. 9999999 ]
Minimum allowed price when selling from this article group and registering the sale via the price (<P>) key.
Enforced for articles that have the entryCode P. For more information, see the articles endpoint.
maximumPrice
integer
[ 0 .. 9999999 ]
Maximum allowed price when selling from this article group and registering the sale via the price (<P>) key.
Enforced for articles that have the entryCode P. For more information, see the articles endpoint.
0 in this field means no maximum price check.
account
string
[ 0 .. 10 ] characters
Specify relevant finance accounts for audit journals. Ask your organization's finance department if you're in doubt about which accounts to use.
omitFromTurnover
integer
Enum:
0
1
If this field has the value 1, the article group in question is NOT included in "net turnover" reporting (common.oms & f100.oms). Ask your Fiftytwo consultant if you're in doubt.
statistics
integer
[ 0 .. 99 ]
Optional field for statistical use. For example, if you want to deliver data about the article group to SPSS, a statistics platform from IBM, you can include a reference to an SPSS accumulator in this field.
joinId
integer
[ 0 .. 99 ]
This field is used to tie articles that have the same
price
discount number
discount amount
together in a virtual family. This means that articles that have the same aggregate discount code via the article group, and whose previously listed fields match, can be mixed and still qualify for the discount.
receiptGroup
integer
[ 0 .. 99 ]
Receipt groups, a.k.a. print groups, define how articles are sorted on receipts (for example into logical sections like Beverages, Frozen food, etc.). The groups are maintained by Fiftytwo consultants.
specialShelfLabel
integer
Enum:
0
1
You can use this parameter to indicate that the article group concerns fruit&vegetables as thus requires special shelf label treatment.
grossProfitLimit
number
decimal places <= 1
[ 0 .. 100 ]
A gross profit limit provides pricing assistance in 52ViKING user interfaces, so that prices that don't meet the gross profit requirement aren't allowed.
maximumPayout
integer
[ 0 .. 9999999 ]
Maximum amount that can be paid out on articles that have the entryCode value P and the applyNegativePrice value 2 (see the articles endpoint for more information). The maximum payout limit doesn't apply to returns.
Example: Lotto winnings.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/articlegroups/v1/chain/{chain}/store/{store}
https://api.fiftytwo.com
/articlegroups/v1/chain/{chain}/store/{store}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"articleGroups"
:
[
{
"articleGroupId"
:
10
,
"description"
:
"Article Group 10"
,
"department"
:
"10"
,
"discountPercentage"
:
15
,
"minimumPrice"
:
10
,
"maximumPrice"
:
20
,
"account"
:
"**********"
,
"omitFromTurnover"
:
1
,
"statistics"
:
1
,
"joinId"
:
10
,
"receiptGroup"
:
1
,
"specialShelfLabel"
:
0
,
"grossProfitLimit"
:
22.5
,
"maximumPayout"
:
110
}
,
{
"articleGroupId"
:
11
,
"description"
:
"Article Group 11"
,
"department"
:
"11"
,
"discountPercentage"
:
20
,
"minimumPrice"
:
100
,
"maximumPrice"
:
200
,
"account"
:
"**********"
,
"omitFromTurnover"
:
0
,
"statistics"
:
0
,
"joinId"
:
11
,
"receiptGroup"
:
2
,
"specialShelfLabel"
:
0
,
"grossProfitLimit"
:
20
,
"maximumPayout"
:
111
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Insert/update list of article groups for all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
Request Body schema:
application/json
articleGroups
required
Array of
objects
Array
articleGroupId
required
integer
[ 1 .. 999999 ]
ID of the article group.
description
string
[ 0 .. 30 ] characters
A short text that describes the article group.
department
string
[ 1 .. 999999 ]
The department that the article group in question belongs to.
discountPercentage
integer
[ 0 .. 10000 ]
Discount percentage that applies to all articles in the article group, where discountPercentage is -1. For more information, see the articles endpoint.
minimumPrice
integer
[ 0 .. 9999999 ]
Minimum allowed price when selling from this article group and registering the sale via the price (<P>) key.
Enforced for articles that have the entryCode P. For more information, see the articles endpoint.
maximumPrice
integer
[ 0 .. 9999999 ]
Maximum allowed price when selling from this article group and registering the sale via the price (<P>) key.
Enforced for articles that have the entryCode P. For more information, see the articles endpoint.
0 in this field means no maximum price check.
account
string
[ 0 .. 10 ] characters
Specify relevant finance accounts for audit journals. Ask your organization's finance department if you're in doubt about which accounts to use.
omitFromTurnover
integer
Enum:
0
1
If this field has the value 1, the article group in question is NOT included in "net turnover" reporting (common.oms & f100.oms). Ask your Fiftytwo consultant if you're in doubt.
statistics
integer
[ 0 .. 99 ]
Optional field for statistical use. For example, if you want to deliver data about the article group to SPSS, a statistics platform from IBM, you can include a reference to an SPSS accumulator in this field.
joinId
integer
[ 0 .. 99 ]
This field is used to tie articles that have the same
price
discount number
discount amount
together in a virtual family. This means that articles that have the same aggregate discount code via the article group, and whose previously listed fields match, can be mixed and still qualify for the discount.
receiptGroup
integer
[ 0 .. 99 ]
Receipt groups, a.k.a. print groups, define how articles are sorted on receipts (for example into logical sections like Beverages, Frozen food, etc.). The groups are maintained by Fiftytwo consultants.
specialShelfLabel
integer
Enum:
0
1
You can use this parameter to indicate that the article group concerns fruit&vegetables as thus requires special shelf label treatment.
grossProfitLimit
number
decimal places <= 1
[ 0 .. 100 ]
A gross profit limit provides pricing assistance in 52ViKING user interfaces, so that prices that don't meet the gross profit requirement aren't allowed.
maximumPayout
integer
[ 0 .. 9999999 ]
Maximum amount that can be paid out on articles that have the entryCode value P and the applyNegativePrice value 2 (see the articles endpoint for more information). The maximum payout limit doesn't apply to returns.
Example: Lotto winnings.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/articlegroups/v1/chain/{chain}
https://api.fiftytwo.com
/articlegroups/v1/chain/{chain}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"articleGroups"
:
[
{
"articleGroupId"
:
10
,
"description"
:
"Article Group 10"
,
"department"
:
"10"
,
"discountPercentage"
:
15
,
"minimumPrice"
:
10
,
"maximumPrice"
:
20
,
"account"
:
"**********"
,
"omitFromTurnover"
:
1
,
"statistics"
:
1
,
"joinId"
:
10
,
"receiptGroup"
:
1
,
"specialShelfLabel"
:
0
,
"grossProfitLimit"
:
22.5
,
"maximumPayout"
:
110
}
,
{
"articleGroupId"
:
11
,
"description"
:
"Article Group 11"
,
"department"
:
"11"
,
"discountPercentage"
:
20
,
"minimumPrice"
:
100
,
"maximumPrice"
:
200
,
"account"
:
"**********"
,
"omitFromTurnover"
:
0
,
"statistics"
:
0
,
"joinId"
:
11
,
"receiptGroup"
:
2
,
"specialShelfLabel"
:
0
,
"grossProfitLimit"
:
20
,
"maximumPayout"
:
111
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete article group from specific store
Not supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
groupId
required
integer
<int64>
The ID of the article group
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/articlegroups/v1/chain/{chain}/store/{store}/{groupId}
https://api.fiftytwo.com
/articlegroups/v1/chain/{chain}/store/{store}/{groupId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete article group from all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
groupId
required
integer
<int64>
The ID of the article group
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/articlegroups/v1/chain/{chain}/{groupId}
https://api.fiftytwo.com
/articlegroups/v1/chain/{chain}/{groupId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
ArticleLists
Article lists can contain any articles that you find relevant for a particular purpose, typically for use in a discount group.
Articles in an article list don't need to have the same price, belong in the same department, or have anything else in common. That gives you freedom to group together any articles that you find relevant.
For example, some organizations use article lists to define articles that by law mustn't be paid for with a particular payment method, which may in some countries be the case with over-the-counter medicines.
Get article list by ID, chain, and store
path Parameters
ArticleListId
required
integer
<int64>
The ID of the article list to retrieve
chainId
required
integer
<int32>
The ID of the chain
storeId
required
integer
<int32>
The ID of the store
Responses
200
Success
Response Schema:
application/json
articleLists
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/articlelists/v1/chain/{chainId}/store/{storeId}/articlelist/{ArticleListId}
https://api.fiftytwo.com
/articlelists/v1/chain/{chainId}/store/{storeId}/articlelist/{ArticleListId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"articleLists"
:
[
{
"articleListId"
:
12345678
,
"description"
:
"articleList 12345678"
,
"articles"
:
[
1
,
2
,
3
,
4
]
}
,
{
"articleListId"
:
87654321
,
"description"
:
"articleList 87654321"
,
"articles"
:
[
5
,
6
,
7
,
8
]
}
]
}
Get article list by ID and chain
path Parameters
ArticleListId
required
integer
<int64>
The ID of the article list to retrieve
chainId
required
integer
<int32>
The ID of the chain
Responses
200
Success
Response Schema:
application/json
articleLists
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/articlelists/v1/chain/{chainId}/{ArticleListId}
https://api.fiftytwo.com
/articlelists/v1/chain/{chainId}/{ArticleListId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"articleLists"
:
[
{
"articleListId"
:
12345678
,
"description"
:
"articleList 12345678"
,
"articles"
:
[
1
,
2
,
3
,
4
]
}
,
{
"articleListId"
:
87654321
,
"description"
:
"articleList 87654321"
,
"articles"
:
[
5
,
6
,
7
,
8
]
}
]
}
Insert/update list of article lists for specific store
Not supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
Request Body schema:
application/json
articleLists
required
Array of
objects
Array
articleListId
required
integer
[ 1 .. ******** ]
ID of the article list.
description
string
[ 0 .. 20 ] characters
A short text that describes the article list.
articles
required
Array of
integers
<= 75000 items
[ items
[ 1 .. ************* ]
]
List of article IDs to associate with the article list.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/articlelists/v1/chain/{chain}/store/{store}
https://api.fiftytwo.com
/articlelists/v1/chain/{chain}/store/{store}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"articleLists"
:
[
{
"articleListId"
:
12345678
,
"description"
:
"articleList 12345678"
,
"articles"
:
[
1
,
2
,
3
,
4
]
}
,
{
"articleListId"
:
87654321
,
"description"
:
"articleList 87654321"
,
"articles"
:
[
5
,
6
,
7
,
8
]
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Insert/update list of article lists for all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
Request Body schema:
application/json
articleLists
required
Array of
objects
Array
articleListId
required
integer
[ 1 .. ******** ]
ID of the article list.
description
string
[ 0 .. 20 ] characters
A short text that describes the article list.
articles
required
Array of
integers
<= 75000 items
[ items
[ 1 .. ************* ]
]
List of article IDs to associate with the article list.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/articlelists/v1/chain/{chain}
https://api.fiftytwo.com
/articlelists/v1/chain/{chain}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"articleLists"
:
[
{
"articleListId"
:
12345678
,
"description"
:
"articleList 12345678"
,
"articles"
:
[
1
,
2
,
3
,
4
]
}
,
{
"articleListId"
:
87654321
,
"description"
:
"articleList 87654321"
,
"articles"
:
[
5
,
6
,
7
,
8
]
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete article list from specific store
Not supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
groupId
required
integer
<int64>
The ID of the article list
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/articlelists/v1/chain/{chain}/store/{store}/{groupId}
https://api.fiftytwo.com
/articlelists/v1/chain/{chain}/store/{store}/{groupId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete article list from all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
groupId
required
integer
<int64>
The ID of the article list
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/articlelists/v1/chain/{chain}/{groupId}
https://api.fiftytwo.com
/articlelists/v1/chain/{chain}/{groupId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Articles
If you use 52ViKING enterprise service distribution, you can use the article/chain/{chain} method to set data that's common for all stores in a single request.
You can use article/chain/{chain}/store/{store} to set prices that take precedence for the specific store. Note that only existing articles can be addressed, and that only the subset of article data listed in the following is extracted with article/chain/{chain}/store/{store}:
familynumber
price
costprice
entryCode
discountQuantity
discountCode
discountAmount
orderingNumber
orderingId
Get article by ID, chain, and store
path Parameters
articleId
required
integer
<int64>
The ID of the article to retrieve
chainId
required
integer
<int32>
The ID of the chain
storeId
required
integer
<int32>
The ID of the store
Responses
200
Success
Response Schema:
application/json
articles
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/articles/v1/chain/{chainId}/store/{storeId}/{articleId}
https://api.fiftytwo.com
/articles/v1/chain/{chainId}/store/{storeId}/{articleId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"articles"
:
[
{
"articleId"
:
200001
,
"price"
:
0
,
"articleTextReceipt"
:
"Price variable article."
,
"articleSupplementaryText"
:
"Packageprice in barcode is used if present. Otherwise Cashier is prompted for packageprice."
,
"repeatAllowed"
:
0
,
"entryCode"
:
"P"
}
,
{
"articleId"
:
210001
,
"price"
:
10
,
"articleTextReceipt"
:
"Price variable conversion."
,
"articleSupplementaryText"
:
"Package price in barcode is used if present. Otherwise Cashier is prompted for packageprice."
,
"numberOfDecimals"
:
2
,
"repeatAllowed"
:
0
,
"entryCode"
:
"P"
}
,
{
"articleId"
:
250001
,
"price"
:
20
,
"articleTextReceipt"
:
"Weight variable article."
,
"articleSupplementaryText"
:
"Weight (3 decimals) from barcode if present otherwise cashier is prompted. Price per weightunit."
,
"packageOrUnitPrice"
:
0
,
"numberOfDecimals"
:
3
,
"repeatAllowed"
:
0
,
"entryCode"
:
"E"
}
,
{
"articleId"
:
260001
,
"price"
:
20
,
"articleTextReceipt"
:
"Weight variable article."
,
"articleSupplementaryText"
:
"Weight (3 decimals) from barcode if present otherwise cashier is prompted. Price per package."
,
"packageOrUnitPrice"
:
1
,
"numberOfDecimals"
:
3
,
"repeatAllowed"
:
0
,
"entryCode"
:
"E"
}
,
{
"articleId"
:
16
,
"articleTextReceipt"
:
"Article 16."
,
"articleSupplementaryText"
:
"Supplementary test"
,
"articleSupplementaryTextType"
:
1
,
"articleGroupId"
:
10
,
"articleFamilyId"
:
0
,
"category"
:
11
,
"assortment"
:
12
,
"statisticsCode"
:
13
,
"vatCode"
:
1
,
"numberOfDecimals"
:
0
,
"entryCode"
:
"E"
,
"staffDiscountCode"
:
1
,
"additionalTextSegment"
:
8000
,
"costPrice"
:
10
,
"price"
:
10
,
"multiplyAllowed"
:
1
,
"repeatAllowed"
:
1
,
"erpArticleId"
:
111111
,
"packageOrUnitPrice"
:
0
,
"totalDiscountFlag"
:
1
,
"salesRule"
:
0
,
"voucherProvider"
:
0
,
"applyNegativePrice"
:
0
,
"additionInformation"
:
0
,
"statisticsCode1"
:
0
,
"statisticsCode2"
:
0
,
"linkedArticle"
:
0
,
"savingStampsEnabled"
:
0
,
"indicativePrice"
:
15.12
,
"assortment1"
:
0
,
"assortment2"
:
0
,
"salesGroupId"
:
0
,
"displayGroup"
:
0
,
"PLC"
:
99
,
"discount"
:
{
"discountQuantity"
:
5
,
"discountCode"
:
1
,
"discountAmount"
:
10
,
"discountPercentage"
:
0
,
"discountInSets"
:
1
,
"discountMaximum"
:
20
,
"clubId"
:
5
}
,
"foodAndBeverage"
:
{
"isAddition"
:
0
,
"articleType"
:
0
,
"isIngredient"
:
0
}
,
"reorder"
:
{
"supplierId"
:
**********12
,
"orderingId"
:
**********1
,
"manufacturer"
:
0
,
"minimumStock"
:
1
,
"maximumStock"
:
2
,
"minimumOrderQuantity"
:
3
,
"unitsPerPackage"
:
6
}
,
"control"
:
{
"disableFromExtendedDiscounts"
:
0
,
"manualDiscountNotAllowed"
:
0
,
"priceChangeNotAllowed"
:
0
,
"blockCashierPriceChange"
:
0
,
"enableOrderChangeNotification"
:
0
,
"shelfLabelPrint"
:
0
,
"stockPrinterNumber"
:
0
,
"manageStock"
:
0
}
,
"shelfLabel"
:
{
"labelText1"
:
"MIDD.TALL., BL�"
,
"labelText2"
:
"NOORA"
,
"labelText3"
:
""
,
"labelText4"
:
""
,
"origin"
:
""
,
"classification"
:
""
,
"salesUnit"
:
{
"content"
:
10
,
"unit"
:
"cm"
}
,
"baseUnit1"
:
"m"
,
"baseUnit2"
:
"mm"
,
"print"
:
{
"labelType"
:
1
,
"labelCopies"
:
1
,
"signType"
:
1
,
"signCopies"
:
1
}
,
"material"
:
1
,
"color"
:
1
,
"size"
:
1
}
,
"barcodes"
:
[
{
"barcode"
:
2008
,
"quantity"
:
3
}
,
{
"barcode"
:
2010
,
"quantity"
:
2
}
]
}
]
}
Get article by ID and chain
path Parameters
articleId
required
integer
<int64>
The ID of the article to retrieve
chainId
required
integer
<int32>
The ID of the chain
Responses
200
Success
Response Schema:
application/json
articles
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/articles/v1/chain/{chainId}/{articleId}
https://api.fiftytwo.com
/articles/v1/chain/{chainId}/{articleId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"articles"
:
[
{
"articleId"
:
200001
,
"price"
:
0
,
"articleTextReceipt"
:
"Price variable article."
,
"articleSupplementaryText"
:
"Packageprice in barcode is used if present. Otherwise Cashier is prompted for packageprice."
,
"repeatAllowed"
:
0
,
"entryCode"
:
"P"
}
,
{
"articleId"
:
210001
,
"price"
:
10
,
"articleTextReceipt"
:
"Price variable conversion."
,
"articleSupplementaryText"
:
"Package price in barcode is used if present. Otherwise Cashier is prompted for packageprice."
,
"numberOfDecimals"
:
2
,
"repeatAllowed"
:
0
,
"entryCode"
:
"P"
}
,
{
"articleId"
:
250001
,
"price"
:
20
,
"articleTextReceipt"
:
"Weight variable article."
,
"articleSupplementaryText"
:
"Weight (3 decimals) from barcode if present otherwise cashier is prompted. Price per weightunit."
,
"packageOrUnitPrice"
:
0
,
"numberOfDecimals"
:
3
,
"repeatAllowed"
:
0
,
"entryCode"
:
"E"
}
,
{
"articleId"
:
260001
,
"price"
:
20
,
"articleTextReceipt"
:
"Weight variable article."
,
"articleSupplementaryText"
:
"Weight (3 decimals) from barcode if present otherwise cashier is prompted. Price per package."
,
"packageOrUnitPrice"
:
1
,
"numberOfDecimals"
:
3
,
"repeatAllowed"
:
0
,
"entryCode"
:
"E"
}
,
{
"articleId"
:
16
,
"articleTextReceipt"
:
"Article 16."
,
"articleSupplementaryText"
:
"Supplementary test"
,
"articleSupplementaryTextType"
:
1
,
"articleGroupId"
:
10
,
"articleFamilyId"
:
0
,
"category"
:
11
,
"assortment"
:
12
,
"statisticsCode"
:
13
,
"vatCode"
:
1
,
"numberOfDecimals"
:
0
,
"entryCode"
:
"E"
,
"staffDiscountCode"
:
1
,
"additionalTextSegment"
:
8000
,
"costPrice"
:
10
,
"price"
:
10
,
"multiplyAllowed"
:
1
,
"repeatAllowed"
:
1
,
"erpArticleId"
:
111111
,
"packageOrUnitPrice"
:
0
,
"totalDiscountFlag"
:
1
,
"salesRule"
:
0
,
"voucherProvider"
:
0
,
"applyNegativePrice"
:
0
,
"additionInformation"
:
0
,
"statisticsCode1"
:
0
,
"statisticsCode2"
:
0
,
"linkedArticle"
:
0
,
"savingStampsEnabled"
:
0
,
"indicativePrice"
:
15.12
,
"assortment1"
:
0
,
"assortment2"
:
0
,
"salesGroupId"
:
0
,
"displayGroup"
:
0
,
"PLC"
:
99
,
"discount"
:
{
"discountQuantity"
:
5
,
"discountCode"
:
1
,
"discountAmount"
:
10
,
"discountPercentage"
:
0
,
"discountInSets"
:
1
,
"discountMaximum"
:
20
,
"clubId"
:
5
}
,
"foodAndBeverage"
:
{
"isAddition"
:
0
,
"articleType"
:
0
,
"isIngredient"
:
0
}
,
"reorder"
:
{
"supplierId"
:
**********12
,
"orderingId"
:
**********1
,
"manufacturer"
:
0
,
"minimumStock"
:
1
,
"maximumStock"
:
2
,
"minimumOrderQuantity"
:
3
,
"unitsPerPackage"
:
6
}
,
"control"
:
{
"disableFromExtendedDiscounts"
:
0
,
"manualDiscountNotAllowed"
:
0
,
"priceChangeNotAllowed"
:
0
,
"blockCashierPriceChange"
:
0
,
"enableOrderChangeNotification"
:
0
,
"shelfLabelPrint"
:
0
,
"stockPrinterNumber"
:
0
,
"manageStock"
:
0
}
,
"shelfLabel"
:
{
"labelText1"
:
"MIDD.TALL., BL�"
,
"labelText2"
:
"NOORA"
,
"labelText3"
:
""
,
"labelText4"
:
""
,
"origin"
:
""
,
"classification"
:
""
,
"salesUnit"
:
{
"content"
:
10
,
"unit"
:
"cm"
}
,
"baseUnit1"
:
"m"
,
"baseUnit2"
:
"mm"
,
"print"
:
{
"labelType"
:
1
,
"labelCopies"
:
1
,
"signType"
:
1
,
"signCopies"
:
1
}
,
"material"
:
1
,
"color"
:
1
,
"size"
:
1
}
,
"barcodes"
:
[
{
"barcode"
:
2008
,
"quantity"
:
3
}
,
{
"barcode"
:
2010
,
"quantity"
:
2
}
]
}
]
}
Insert/update list of articles for specific store
When you use this method on a 52ViKING enterprise service endpoint, only a subset of article data is supported.
Note that articles must be present for all stores, that is you can't have an article for just
a single store.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
Request Body schema:
application/json
articles
required
Array of
objects
Array
articleId
required
integer
[ 1 .. ************* ]
An article ID is a number that can be constructed based on the following principles:
1) EAN-13
2) EAN-8
3) UPC
4) PLU numbers
5) PLUS numbers
6) Article group or department numbers
7) Own article numbers
articleTextReceipt
string
[ 0 .. 30 ] characters
Text that represents the article on receipts. Can, in addition to receipt text, contain the following:
Reference to other article number. Written like this:
=>**********123, where **********123 is the article number that's referred to
=>57 * 6, where the reference sells 6 units of article number 57.
When selling the article number in question, the article number is converted to the referenced number, and the PLU information of the referenced number is used.
Note that this reference mechanism can also be achieved using the barcodes segment.
Reference to means of payment. Used when scanning bottle redemption slips and similar. The entry code for this article must be "B". The means of payment code is indicated by the three first characters. The remaining characters are for general use (not printed on receipt). Example: 500 bottle redemption slip (means of payment 500 must exist)
Receipt text: The current number of characters that can be printed on receipts is controlled by '>b' in VB10.
articleSupplementaryText
string
[ 0 .. 96 ] characters
Used for article-related information for shop assiatstants or customers. Example:
This article needs 3 AA batteries to work
Use articleSupplementaryTextType to define where the information is shown.
articleSupplementaryTextType
integer
[ 0 .. 9 ]
Defines how the articleSupplementaryText is displayed. This depends on definition of text segments for articles, and should be agreed with your Fiftytwo consultant. Suggested values are:
1. Show on shop assistant display
2. Print on receipt and show on shop assistant display
A value of 0 will replace the articleTextReceipt on the shop assistant display. This is useful when longer texts are needed for an article and can be done without adding text sections to the article.
articleFamilyId
integer
[ 0 .. ******** ]
A reference number that ties together related articles with different article numbers. Used for triggering quantity discounts across article numbers. Use discount families to maintain such master data.
articleGroupId
integer
[ 1 .. 999999 ]
A reference number that ties the article to an article group. The fact that an article belongs in article group can be used for many purposes, such as article group-based discounts, article group-based statistics, or article group-based thresholds (for example how many articles from an article group that shop assistants can sell with a POS's multiply function). You maintain article groups via the articleGroup endpoint.
category
integer
[ 1 .. 99 ]
assortment
integer
[ 0 .. 99 ]
statisticsCode
integer
[ 0 .. 999 ]
vatCode
integer
[ 0 .. 9 ]
Used for the following purposes:
Deduction of VAT for export sales
Recalculation of price in case of VAT changes
Specification of VAT for special customers
Calculation of gross profit on articles as well as departments/article groups
numberOfDecimals
integer
[ 0 .. 9 ]
Defines the number of decimals that can be entered on the POS. For articles with barcodes that are defined as weight-variable, the field is used to indicate the position of the comma in the barcode.
Example: 2622220036145 leads to article number 262222 in a quantity of 3,614 if the number of decimals is set to 3. The price of the article must then be set up as price per kilo.
        You can use the following values:
0 decimals
1 decimal
2 decimals
3 decimals
Note that the definition of the barcode in _EAN must match this setting. If no weight is configured for the barcode and this setting is > 0, the shop assistant will be prompted for weight information.
entryCode
string
Enum:
"E"
"P"
"X"
"B"
Defines if it's allowed to enter prices on the POS.
E = Price must not be entered on POS. Article is sold at the price defined in the price field.
P = Price can be entered on POS. If price is 0, a new price must be entered. Otherwise a price change is optional on the POS.
X = Article must not be sold. This may apply to raw materials or articles on pallets.
B = The scanner number is a means of payment. The means of payment code must be defined in the receipt text.
staffDiscountCode
integer
[ 0 .. 9 ]
A reference to the rate to use for calculation of staff discount.
         You can use the following values:
1 = Staff discount rate 1.
2 = Staff discount rate 2
3 = Staff discount rate 3
4 = Staff discount rate 4
5 = Staff discount rate 5
6 = Staff discount rate 6
7 = Staff discount rate 7
8 = Staff discount rate 8
9 = Staff discount rate 9
0 = No staff discount rate
You define staff discount rates in Bpar.prapct. Ask your Fiftytwo consultant if you're in doubt.
additionalTextSegment
integer
[ 0 .. 9999 ]
Four-digit number of a text section (VB10). Text sections must be within the interval 8000 to 9999. The text section defines where the text is printed on the receipt, either directly beneath the article line (for example a serial number) or at the bottom of the receipt (for example a warranty certificate). Text sections are typically maintained bu Fiftytwo consultants. Ask your Fiftytwo consultant if you're in doubt.
costPrice
number
decimal places <= 2
The supplier's cost price. Cost price must be including or excluding VAT according to Bpar.kpmoms (ask your Fiftytwo consultant if you're in doubt). A negative value indicates expected gross profit percentage.
price
number
decimal places <= 2
Sales price. Normally the sales price of one unit of the article. See, however, also packageOrUnitPrice, entryCode, and numberOfDecimals.
multiplyAllowed
integer
[ 0 .. 3 ]
Defines if use of a POS's multiply key (X) is allowed for the article.
0 = No, quantity must never be specified. May, for example, be relevant for articles where you need to register additional individual information, such as a serial number.
1 = Yes, quantity can be specified.
2 = Yes, quantity must always be specified. Used for articles that are not normally sold as one unit, for example articles that are sold by running meter or by weight.
3 = Quantity can be specified, but content must always be specified. Used for articles that have different content, for example planks of wood that have the same article number regardless of length. The article can then be sold as, for example, 10 planks of 2,70 meters. Quantity of articles is registered with the (X) key, and the POS then asks for the length or similar.
repeatAllowed
integer
Enum:
0
1
Defines if use of a POS's repeat last line key (REPEAT) is allowed for the article.
0 = No
1 = Yes
erpArticleId
integer
[ 0 .. ************* ]
The ID of the article in your organization's ERP system.
packageOrUnitPrice
integer
[ 0 .. 3 ]
Defines the following:
Sales: Only has impact on weight-variable articles, where the field defines whether the specified price is a price per kilo or a fixed price for one pack.
Returns: Simulates return method 0 for the article in question, regardless of which return method is otherwise defined.
Valid values are:
0 = Price per kilo
1 = Pack price, regardless of content
2 = Simulate return method 0 according to PA02
3 = 1 and 2
Note: If you want to give a discount on an article where the barcode contains weight information, you must use discountCode 4 or 5.
totalDiscountFlag
integer
Enum:
0
1
Defines if the article in question must trigger a specific discount.
0 = No
1 = Yes
The discount information is fetched from the PA04 parameters.
salesRule
integer
[ 0 .. 9 ]
Reference to a sales rule. A sales rule can trigger a notification that shop assistants must respond to. Definition of sales rules is handled by Fiftytwo consultants
voucherProvider
integer
[ 0 .. 9 ]
A voucher article is a non-physical article (for example talk time for a mobile phone) that's delivered by printing a voucher on the POS printer. The voucher typically contains an activation code. The individual article instance is associated with a voucher number that provides access to a separate record that contains the data (for example the activation code) that's associated with the individual article instance in question. The voucher number is unique across article numbers. This field defines if the article in question is a voucher article, and, if that's the case, which provider it comes from.
0 = Not a voucher article
1 = Goyada
2 = Top Up
3 = Lotto
4 = Payzone
5 = REA gift card
6 = Blackhawk gift card
7 = Goyada, new method
8 = Blackhawk POR (Print On Request) / DAR (Digtital Account Request)
9 = Epay POR (Print On Request)
10 = Epay closed loop (external giftcards with balance)
11 = Epay POSA (Point Of Sale Activation)
90 = Downpayment
applyNegativePrice
integer
Enum:
0
1
If selected, the price gets an opposite sign, that is negative instead of positive. Can, for example, be used for bottle deposit refunds
additionInformation
integer
Enum:
0
1
Registration of additional information.
0 = No additional information
1 = Goods intake date. The information must be part of the barcode and can be defined via _EAN field type "d"
statisticsCode1
integer
[ 0 .. 999 ]
statisticsCode2
integer
[ 0 .. 999 ]
linkedArticle
integer
[ 0 .. ************* ]
Possibility of defining a 13-digit linked article number, which will then automatically be added to the sale when article is sold. Examples: Bottle deposit for a soft drink (where the bottle deposit is the linked article). Compulsory safety net for a trampoline (where the safety net is the linked article).
savingStampsEnabled
integer
Enum:
0
1
Defines if the article in question should be included in calculation of savings stamps. This flag is only used if Bpar.mrkflg=1.
0 = No, don't include the article
1 = Yes, include the article
indicativePrice
number
decimal places <= 2
The article's suggested retail price or standard price.
assortment1
integer
[ 0 .. 9999 ]
assortment2
integer
[ 0 .. 9999 ]
salesGroupId
integer
[ 0 .. 999 ]
Sales grouping with possibility of defining a maximum allowed sale within individual sales groupings. Primarily used for limiting sales of over-the-counter medicines.
The sales group ID is a reference to an existing sales group. Sales groups are maintained using the Salesgroups endpoints.
displayGroup
integer
[ 0 .. 99 ]
Diaplay groups can be used for automatic creation of easy access links on some POS units. If != 0 the display group that an article belongs in. Ask your Fiftytwo consultant if you're in doubt.
PLC
integer
[ 0 .. 99 ]
Product life cycle code. Used for filtering out articles in queries from, for example, POSs.
discount
object
foodAndBeverage
object
reorder
object
Only used if 52ViKING handles reordering. Ask your Fiftytwo consultant if you're in doubt.
control
object
barcodes
Array of
objects
A list of articleIds that point to the articleId. You can associate the article with one or multiple barcodes, and specify a quantity associated with each barcode. Example: You want to associate an article with two barcodes: One for a single unit of the article (the barcode on the article itself) and one for a box of 24 units of the article (the barcode on the 24-unit box).
To remove all barcodes pointing to an article, use an empty barcodes array.
shelfLabel
object
You can specify shelf-edge label settings for the article. Depending on the setup of your shelf-edge label system, shelf-edge labels will then be printed, marked for printing, or updated (if you use electronic shelf labels, ESLs) when you change article information, such as price, name, etc. Ask your Fiftytwo consultant if you're in doubt.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/articles/v1/chain/{chain}/store/{store}
https://api.fiftytwo.com
/articles/v1/chain/{chain}/store/{store}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"articles"
:
[
{
"articleId"
:
200001
,
"price"
:
0
,
"articleTextReceipt"
:
"Price variable article."
,
"articleSupplementaryText"
:
"Packageprice in barcode is used if present. Otherwise Cashier is prompted for packageprice."
,
"repeatAllowed"
:
0
,
"entryCode"
:
"P"
}
,
{
"articleId"
:
210001
,
"price"
:
10
,
"articleTextReceipt"
:
"Price variable conversion."
,
"articleSupplementaryText"
:
"Package price in barcode is used if present. Otherwise Cashier is prompted for packageprice."
,
"numberOfDecimals"
:
2
,
"repeatAllowed"
:
0
,
"entryCode"
:
"P"
}
,
{
"articleId"
:
250001
,
"price"
:
20
,
"articleTextReceipt"
:
"Weight variable article."
,
"articleSupplementaryText"
:
"Weight (3 decimals) from barcode if present otherwise cashier is prompted. Price per weightunit."
,
"packageOrUnitPrice"
:
0
,
"numberOfDecimals"
:
3
,
"repeatAllowed"
:
0
,
"entryCode"
:
"E"
}
,
{
"articleId"
:
260001
,
"price"
:
20
,
"articleTextReceipt"
:
"Weight variable article."
,
"articleSupplementaryText"
:
"Weight (3 decimals) from barcode if present otherwise cashier is prompted. Price per package."
,
"packageOrUnitPrice"
:
1
,
"numberOfDecimals"
:
3
,
"repeatAllowed"
:
0
,
"entryCode"
:
"E"
}
,
{
"articleId"
:
16
,
"articleTextReceipt"
:
"Article 16."
,
"articleSupplementaryText"
:
"Supplementary test"
,
"articleSupplementaryTextType"
:
1
,
"articleGroupId"
:
10
,
"articleFamilyId"
:
0
,
"category"
:
11
,
"assortment"
:
12
,
"statisticsCode"
:
13
,
"vatCode"
:
1
,
"numberOfDecimals"
:
0
,
"entryCode"
:
"E"
,
"staffDiscountCode"
:
1
,
"additionalTextSegment"
:
8000
,
"costPrice"
:
10
,
"price"
:
10
,
"multiplyAllowed"
:
1
,
"repeatAllowed"
:
1
,
"erpArticleId"
:
111111
,
"packageOrUnitPrice"
:
0
,
"totalDiscountFlag"
:
1
,
"salesRule"
:
0
,
"voucherProvider"
:
0
,
"applyNegativePrice"
:
0
,
"additionInformation"
:
0
,
"statisticsCode1"
:
0
,
"statisticsCode2"
:
0
,
"linkedArticle"
:
0
,
"savingStampsEnabled"
:
0
,
"indicativePrice"
:
15.12
,
"assortment1"
:
0
,
"assortment2"
:
0
,
"salesGroupId"
:
0
,
"displayGroup"
:
0
,
"PLC"
:
99
,
"discount"
:
{
"discountQuantity"
:
5
,
"discountCode"
:
1
,
"discountAmount"
:
10
,
"discountPercentage"
:
0
,
"discountInSets"
:
1
,
"discountMaximum"
:
20
,
"clubId"
:
5
}
,
"foodAndBeverage"
:
{
"isAddition"
:
0
,
"articleType"
:
0
,
"isIngredient"
:
0
}
,
"reorder"
:
{
"supplierId"
:
**********12
,
"orderingId"
:
**********1
,
"manufacturer"
:
0
,
"minimumStock"
:
1
,
"maximumStock"
:
2
,
"minimumOrderQuantity"
:
3
,
"unitsPerPackage"
:
6
}
,
"control"
:
{
"disableFromExtendedDiscounts"
:
0
,
"manualDiscountNotAllowed"
:
0
,
"priceChangeNotAllowed"
:
0
,
"blockCashierPriceChange"
:
0
,
"enableOrderChangeNotification"
:
0
,
"shelfLabelPrint"
:
0
,
"stockPrinterNumber"
:
0
,
"manageStock"
:
0
}
,
"shelfLabel"
:
{
"labelText1"
:
"MIDD.TALL., BL�"
,
"labelText2"
:
"NOORA"
,
"labelText3"
:
""
,
"labelText4"
:
""
,
"origin"
:
""
,
"classification"
:
""
,
"salesUnit"
:
{
"content"
:
10
,
"unit"
:
"cm"
}
,
"baseUnit1"
:
"m"
,
"baseUnit2"
:
"mm"
,
"print"
:
{
"labelType"
:
1
,
"labelCopies"
:
1
,
"signType"
:
1
,
"signCopies"
:
1
}
,
"material"
:
1
,
"color"
:
1
,
"size"
:
1
}
,
"barcodes"
:
[
{
"barcode"
:
2008
,
"quantity"
:
3
}
,
{
"barcode"
:
2010
,
"quantity"
:
2
}
]
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Insert/update list of articles for all stores
Only supported for 52ViKING enterprise service endpoints.
path Parameters
chain
required
integer
<int32>
The ID of the Chain
Request Body schema:
application/json
articles
required
Array of
objects
Array
articleId
required
integer
[ 1 .. ************* ]
An article ID is a number that can be constructed based on the following principles:
1) EAN-13
2) EAN-8
3) UPC
4) PLU numbers
5) PLUS numbers
6) Article group or department numbers
7) Own article numbers
articleTextReceipt
string
[ 0 .. 30 ] characters
Text that represents the article on receipts. Can, in addition to receipt text, contain the following:
Reference to other article number. Written like this:
=>**********123, where **********123 is the article number that's referred to
=>57 * 6, where the reference sells 6 units of article number 57.
When selling the article number in question, the article number is converted to the referenced number, and the PLU information of the referenced number is used.
Note that this reference mechanism can also be achieved using the barcodes segment.
Reference to means of payment. Used when scanning bottle redemption slips and similar. The entry code for this article must be "B". The means of payment code is indicated by the three first characters. The remaining characters are for general use (not printed on receipt). Example: 500 bottle redemption slip (means of payment 500 must exist)
Receipt text: The current number of characters that can be printed on receipts is controlled by '>b' in VB10.
articleSupplementaryText
string
[ 0 .. 96 ] characters
Used for article-related information for shop assiatstants or customers. Example:
This article needs 3 AA batteries to work
Use articleSupplementaryTextType to define where the information is shown.
articleSupplementaryTextType
integer
[ 0 .. 9 ]
Defines how the articleSupplementaryText is displayed. This depends on definition of text segments for articles, and should be agreed with your Fiftytwo consultant. Suggested values are:
1. Show on shop assistant display
2. Print on receipt and show on shop assistant display
A value of 0 will replace the articleTextReceipt on the shop assistant display. This is useful when longer texts are needed for an article and can be done without adding text sections to the article.
articleFamilyId
integer
[ 0 .. ******** ]
A reference number that ties together related articles with different article numbers. Used for triggering quantity discounts across article numbers. Use discount families to maintain such master data.
articleGroupId
integer
[ 1 .. 999999 ]
A reference number that ties the article to an article group. The fact that an article belongs in article group can be used for many purposes, such as article group-based discounts, article group-based statistics, or article group-based thresholds (for example how many articles from an article group that shop assistants can sell with a POS's multiply function). You maintain article groups via the articleGroup endpoint.
category
integer
[ 1 .. 99 ]
assortment
integer
[ 0 .. 99 ]
statisticsCode
integer
[ 0 .. 999 ]
vatCode
integer
[ 0 .. 9 ]
Used for the following purposes:
Deduction of VAT for export sales
Recalculation of price in case of VAT changes
Specification of VAT for special customers
Calculation of gross profit on articles as well as departments/article groups
numberOfDecimals
integer
[ 0 .. 9 ]
Defines the number of decimals that can be entered on the POS. For articles with barcodes that are defined as weight-variable, the field is used to indicate the position of the comma in the barcode.
Example: 2622220036145 leads to article number 262222 in a quantity of 3,614 if the number of decimals is set to 3. The price of the article must then be set up as price per kilo.
        You can use the following values:
0 decimals
1 decimal
2 decimals
3 decimals
Note that the definition of the barcode in _EAN must match this setting. If no weight is configured for the barcode and this setting is > 0, the shop assistant will be prompted for weight information.
entryCode
string
Enum:
"E"
"P"
"X"
"B"
Defines if it's allowed to enter prices on the POS.
E = Price must not be entered on POS. Article is sold at the price defined in the price field.
P = Price can be entered on POS. If price is 0, a new price must be entered. Otherwise a price change is optional on the POS.
X = Article must not be sold. This may apply to raw materials or articles on pallets.
B = The scanner number is a means of payment. The means of payment code must be defined in the receipt text.
staffDiscountCode
integer
[ 0 .. 9 ]
A reference to the rate to use for calculation of staff discount.
         You can use the following values:
1 = Staff discount rate 1.
2 = Staff discount rate 2
3 = Staff discount rate 3
4 = Staff discount rate 4
5 = Staff discount rate 5
6 = Staff discount rate 6
7 = Staff discount rate 7
8 = Staff discount rate 8
9 = Staff discount rate 9
0 = No staff discount rate
You define staff discount rates in Bpar.prapct. Ask your Fiftytwo consultant if you're in doubt.
additionalTextSegment
integer
[ 0 .. 9999 ]
Four-digit number of a text section (VB10). Text sections must be within the interval 8000 to 9999. The text section defines where the text is printed on the receipt, either directly beneath the article line (for example a serial number) or at the bottom of the receipt (for example a warranty certificate). Text sections are typically maintained bu Fiftytwo consultants. Ask your Fiftytwo consultant if you're in doubt.
costPrice
number
decimal places <= 2
The supplier's cost price. Cost price must be including or excluding VAT according to Bpar.kpmoms (ask your Fiftytwo consultant if you're in doubt). A negative value indicates expected gross profit percentage.
price
number
decimal places <= 2
Sales price. Normally the sales price of one unit of the article. See, however, also packageOrUnitPrice, entryCode, and numberOfDecimals.
multiplyAllowed
integer
[ 0 .. 3 ]
Defines if use of a POS's multiply key (X) is allowed for the article.
0 = No, quantity must never be specified. May, for example, be relevant for articles where you need to register additional individual information, such as a serial number.
1 = Yes, quantity can be specified.
2 = Yes, quantity must always be specified. Used for articles that are not normally sold as one unit, for example articles that are sold by running meter or by weight.
3 = Quantity can be specified, but content must always be specified. Used for articles that have different content, for example planks of wood that have the same article number regardless of length. The article can then be sold as, for example, 10 planks of 2,70 meters. Quantity of articles is registered with the (X) key, and the POS then asks for the length or similar.
repeatAllowed
integer
Enum:
0
1
Defines if use of a POS's repeat last line key (REPEAT) is allowed for the article.
0 = No
1 = Yes
erpArticleId
integer
[ 0 .. ************* ]
The ID of the article in your organization's ERP system.
packageOrUnitPrice
integer
[ 0 .. 3 ]
Defines the following:
Sales: Only has impact on weight-variable articles, where the field defines whether the specified price is a price per kilo or a fixed price for one pack.
Returns: Simulates return method 0 for the article in question, regardless of which return method is otherwise defined.
Valid values are:
0 = Price per kilo
1 = Pack price, regardless of content
2 = Simulate return method 0 according to PA02
3 = 1 and 2
Note: If you want to give a discount on an article where the barcode contains weight information, you must use discountCode 4 or 5.
totalDiscountFlag
integer
Enum:
0
1
Defines if the article in question must trigger a specific discount.
0 = No
1 = Yes
The discount information is fetched from the PA04 parameters.
salesRule
integer
[ 0 .. 9 ]
Reference to a sales rule. A sales rule can trigger a notification that shop assistants must respond to. Definition of sales rules is handled by Fiftytwo consultants
voucherProvider
integer
[ 0 .. 9 ]
A voucher article is a non-physical article (for example talk time for a mobile phone) that's delivered by printing a voucher on the POS printer. The voucher typically contains an activation code. The individual article instance is associated with a voucher number that provides access to a separate record that contains the data (for example the activation code) that's associated with the individual article instance in question. The voucher number is unique across article numbers. This field defines if the article in question is a voucher article, and, if that's the case, which provider it comes from.
0 = Not a voucher article
1 = Goyada
2 = Top Up
3 = Lotto
4 = Payzone
5 = REA gift card
6 = Blackhawk gift card
7 = Goyada, new method
8 = Blackhawk POR (Print On Request) / DAR (Digtital Account Request)
9 = Epay POR (Print On Request)
10 = Epay closed loop (external giftcards with balance)
11 = Epay POSA (Point Of Sale Activation)
90 = Downpayment
applyNegativePrice
integer
Enum:
0
1
If selected, the price gets an opposite sign, that is negative instead of positive. Can, for example, be used for bottle deposit refunds
additionInformation
integer
Enum:
0
1
Registration of additional information.
0 = No additional information
1 = Goods intake date. The information must be part of the barcode and can be defined via _EAN field type "d"
statisticsCode1
integer
[ 0 .. 999 ]
statisticsCode2
integer
[ 0 .. 999 ]
linkedArticle
integer
[ 0 .. ************* ]
Possibility of defining a 13-digit linked article number, which will then automatically be added to the sale when article is sold. Examples: Bottle deposit for a soft drink (where the bottle deposit is the linked article). Compulsory safety net for a trampoline (where the safety net is the linked article).
savingStampsEnabled
integer
Enum:
0
1
Defines if the article in question should be included in calculation of savings stamps. This flag is only used if Bpar.mrkflg=1.
0 = No, don't include the article
1 = Yes, include the article
indicativePrice
number
decimal places <= 2
The article's suggested retail price or standard price.
assortment1
integer
[ 0 .. 9999 ]
assortment2
integer
[ 0 .. 9999 ]
salesGroupId
integer
[ 0 .. 999 ]
Sales grouping with possibility of defining a maximum allowed sale within individual sales groupings. Primarily used for limiting sales of over-the-counter medicines.
The sales group ID is a reference to an existing sales group. Sales groups are maintained using the Salesgroups endpoints.
displayGroup
integer
[ 0 .. 99 ]
Diaplay groups can be used for automatic creation of easy access links on some POS units. If != 0 the display group that an article belongs in. Ask your Fiftytwo consultant if you're in doubt.
PLC
integer
[ 0 .. 99 ]
Product life cycle code. Used for filtering out articles in queries from, for example, POSs.
discount
object
foodAndBeverage
object
reorder
object
Only used if 52ViKING handles reordering. Ask your Fiftytwo consultant if you're in doubt.
control
object
barcodes
Array of
objects
A list of articleIds that point to the articleId. You can associate the article with one or multiple barcodes, and specify a quantity associated with each barcode. Example: You want to associate an article with two barcodes: One for a single unit of the article (the barcode on the article itself) and one for a box of 24 units of the article (the barcode on the 24-unit box).
To remove all barcodes pointing to an article, use an empty barcodes array.
shelfLabel
object
You can specify shelf-edge label settings for the article. Depending on the setup of your shelf-edge label system, shelf-edge labels will then be printed, marked for printing, or updated (if you use electronic shelf labels, ESLs) when you change article information, such as price, name, etc. Ask your Fiftytwo consultant if you're in doubt.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/articles/v1/chain/{chain}
https://api.fiftytwo.com
/articles/v1/chain/{chain}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"articles"
:
[
{
"articleId"
:
200001
,
"price"
:
0
,
"articleTextReceipt"
:
"Price variable article."
,
"articleSupplementaryText"
:
"Packageprice in barcode is used if present. Otherwise Cashier is prompted for packageprice."
,
"repeatAllowed"
:
0
,
"entryCode"
:
"P"
}
,
{
"articleId"
:
210001
,
"price"
:
10
,
"articleTextReceipt"
:
"Price variable conversion."
,
"articleSupplementaryText"
:
"Package price in barcode is used if present. Otherwise Cashier is prompted for packageprice."
,
"numberOfDecimals"
:
2
,
"repeatAllowed"
:
0
,
"entryCode"
:
"P"
}
,
{
"articleId"
:
250001
,
"price"
:
20
,
"articleTextReceipt"
:
"Weight variable article."
,
"articleSupplementaryText"
:
"Weight (3 decimals) from barcode if present otherwise cashier is prompted. Price per weightunit."
,
"packageOrUnitPrice"
:
0
,
"numberOfDecimals"
:
3
,
"repeatAllowed"
:
0
,
"entryCode"
:
"E"
}
,
{
"articleId"
:
260001
,
"price"
:
20
,
"articleTextReceipt"
:
"Weight variable article."
,
"articleSupplementaryText"
:
"Weight (3 decimals) from barcode if present otherwise cashier is prompted. Price per package."
,
"packageOrUnitPrice"
:
1
,
"numberOfDecimals"
:
3
,
"repeatAllowed"
:
0
,
"entryCode"
:
"E"
}
,
{
"articleId"
:
16
,
"articleTextReceipt"
:
"Article 16."
,
"articleSupplementaryText"
:
"Supplementary test"
,
"articleSupplementaryTextType"
:
1
,
"articleGroupId"
:
10
,
"articleFamilyId"
:
0
,
"category"
:
11
,
"assortment"
:
12
,
"statisticsCode"
:
13
,
"vatCode"
:
1
,
"numberOfDecimals"
:
0
,
"entryCode"
:
"E"
,
"staffDiscountCode"
:
1
,
"additionalTextSegment"
:
8000
,
"costPrice"
:
10
,
"price"
:
10
,
"multiplyAllowed"
:
1
,
"repeatAllowed"
:
1
,
"erpArticleId"
:
111111
,
"packageOrUnitPrice"
:
0
,
"totalDiscountFlag"
:
1
,
"salesRule"
:
0
,
"voucherProvider"
:
0
,
"applyNegativePrice"
:
0
,
"additionInformation"
:
0
,
"statisticsCode1"
:
0
,
"statisticsCode2"
:
0
,
"linkedArticle"
:
0
,
"savingStampsEnabled"
:
0
,
"indicativePrice"
:
15.12
,
"assortment1"
:
0
,
"assortment2"
:
0
,
"salesGroupId"
:
0
,
"displayGroup"
:
0
,
"PLC"
:
99
,
"discount"
:
{
"discountQuantity"
:
5
,
"discountCode"
:
1
,
"discountAmount"
:
10
,
"discountPercentage"
:
0
,
"discountInSets"
:
1
,
"discountMaximum"
:
20
,
"clubId"
:
5
}
,
"foodAndBeverage"
:
{
"isAddition"
:
0
,
"articleType"
:
0
,
"isIngredient"
:
0
}
,
"reorder"
:
{
"supplierId"
:
**********12
,
"orderingId"
:
**********1
,
"manufacturer"
:
0
,
"minimumStock"
:
1
,
"maximumStock"
:
2
,
"minimumOrderQuantity"
:
3
,
"unitsPerPackage"
:
6
}
,
"control"
:
{
"disableFromExtendedDiscounts"
:
0
,
"manualDiscountNotAllowed"
:
0
,
"priceChangeNotAllowed"
:
0
,
"blockCashierPriceChange"
:
0
,
"enableOrderChangeNotification"
:
0
,
"shelfLabelPrint"
:
0
,
"stockPrinterNumber"
:
0
,
"manageStock"
:
0
}
,
"shelfLabel"
:
{
"labelText1"
:
"MIDD.TALL., BL�"
,
"labelText2"
:
"NOORA"
,
"labelText3"
:
""
,
"labelText4"
:
""
,
"origin"
:
""
,
"classification"
:
""
,
"salesUnit"
:
{
"content"
:
10
,
"unit"
:
"cm"
}
,
"baseUnit1"
:
"m"
,
"baseUnit2"
:
"mm"
,
"print"
:
{
"labelType"
:
1
,
"labelCopies"
:
1
,
"signType"
:
1
,
"signCopies"
:
1
}
,
"material"
:
1
,
"color"
:
1
,
"size"
:
1
}
,
"barcodes"
:
[
{
"barcode"
:
2008
,
"quantity"
:
3
}
,
{
"barcode"
:
2010
,
"quantity"
:
2
}
]
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete article from specific store
When you use this method on a 52ViKING  enterprise service endpoint, only store-specific data is deleted.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
articleId
required
integer
<int64>
The ID of the article to delete
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/articles/v1/chain/{chain}/store/{store}/{articleId}
https://api.fiftytwo.com
/articles/v1/chain/{chain}/store/{store}/{articleId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete article from all stores
Only supported for 52ViKING enterprise service endpoints.
path Parameters
chain
required
integer
<int32>
The ID of the chain
articleId
required
integer
<int64>
The ID of the article to delete
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/articles/v1/chain/{chain}/{articleId}
https://api.fiftytwo.com
/articles/v1/chain/{chain}/{articleId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Campaigns
A campaign is when specific articles and/or discount families are sold with a specific discount during a specific period of time.
With a campaign, you can grant discounts in the form of cash discounts, percentage discounts, multibuy discounts, X for Y discounts as well as quantity-based discount ladders. You can specify club membership requirements, maximum quantities, how articles in excess of discount-triggering quantities should be handled (example scenario: Buy 3, save EUR 25%; customer buys 4), and much more.
Time management-wise you can control a campaign's start and end date/time (example: From 17 May, 2025 06:30 until 23 May, 2025 23:59). If you need more fine-grained time management for offering discounts, for example limited to certain times on certain days of the week, consider using 52ViKING's highly flexible discount group feature.
Get campaign by ID, chain, and store
path Parameters
campaignId
required
integer
<int64>
The ID of the campaign to retrieve
chainId
required
integer
<int32>
The ID of the chain
storeId
required
integer
<int32>
The ID of the store
Responses
200
Success
Response Schema:
application/json
campaigns
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/campaigns/v1/chain/{chainId}/store/{storeId}/{campaignId}
https://api.fiftytwo.com
/campaigns/v1/chain/{chainId}/store/{storeId}/{campaignId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"campaigns"
:
[
{
"campaignId"
:
1123168
,
"campaignStart"
:
"2017-06-28T22:00:00+00:00"
,
"campaignStop"
:
"2027-06-29T21:59:59+00:00"
,
"campaignType"
:
1
,
"storeGroup"
:
"ABCD"
,
"description"
:
"Test campaign"
,
"campaignFamilies"
:
[
{
"campaignFamilyId"
:
123
,
"description"
:
"campaign family 123"
,
"campaignFamilyDiscount"
:
[
{
"quantity"
:
1
,
"amount"
:
16
}
,
{
"quantity"
:
2
,
"amount"
:
8
}
,
{
"quantity"
:
4
,
"amount"
:
4
}
,
{
"quantity"
:
8
,
"amount"
:
2
}
,
{
"quantity"
:
16
,
"amount"
:
1
}
]
,
"discountInSets"
:
1
,
"discountMaximum"
:
16
}
,
{
"campaignFamilyId"
:
124
,
"description"
:
"campaign family 124"
,
"campaignFamilyDiscount"
:
[
{
"quantity"
:
1
,
"amount"
:
16
}
,
{
"quantity"
:
2
,
"amount"
:
8
}
,
{
"quantity"
:
4
,
"amount"
:
4
}
,
{
"quantity"
:
8
,
"amount"
:
2
}
,
{
"quantity"
:
16
,
"amount"
:
1
}
]
,
"discountInSets"
:
1
,
"discountMaximum"
:
16
}
]
,
"campaignArticles"
:
[
{
"campaignArticleId"
:
10083
,
"costPrice"
:
5
,
"price"
:
10
,
"discountCode"
:
1
,
"discountQuantity"
:
5
,
"discountAmount"
:
10
,
"discountPercentage"
:
0
,
"discountInSets"
:
1
,
"discountMaximum"
:
20
,
"articleFamilyId"
:
0
,
"clubId"
:
5
}
,
{
"campaignArticleId"
:
100087
,
"costPrice"
:
20
,
"price"
:
30
,
"discountCode"
:
2
,
"discountQuantity"
:
1
,
"discountAmount"
:
5
,
"discountPercentage"
:
0
,
"discountInSets"
:
1
,
"discountMaximum"
:
20
,
"articleFamilyId"
:
0
,
"clubId"
:
5
}
]
}
]
}
Get campaign by ID and chain
path Parameters
campaignId
required
integer
<int64>
The ID of the campaign to retrieve
chainId
required
integer
<int32>
The ID of the chain
Responses
200
Success
Response Schema:
application/json
campaigns
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/campaigns/v1/chain/{chainId}/{campaignId}
https://api.fiftytwo.com
/campaigns/v1/chain/{chainId}/{campaignId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"campaigns"
:
[
{
"campaignId"
:
1123168
,
"campaignStart"
:
"2017-06-28T22:00:00+00:00"
,
"campaignStop"
:
"2027-06-29T21:59:59+00:00"
,
"campaignType"
:
1
,
"storeGroup"
:
"ABCD"
,
"description"
:
"Test campaign"
,
"campaignFamilies"
:
[
{
"campaignFamilyId"
:
123
,
"description"
:
"campaign family 123"
,
"campaignFamilyDiscount"
:
[
{
"quantity"
:
1
,
"amount"
:
16
}
,
{
"quantity"
:
2
,
"amount"
:
8
}
,
{
"quantity"
:
4
,
"amount"
:
4
}
,
{
"quantity"
:
8
,
"amount"
:
2
}
,
{
"quantity"
:
16
,
"amount"
:
1
}
]
,
"discountInSets"
:
1
,
"discountMaximum"
:
16
}
,
{
"campaignFamilyId"
:
124
,
"description"
:
"campaign family 124"
,
"campaignFamilyDiscount"
:
[
{
"quantity"
:
1
,
"amount"
:
16
}
,
{
"quantity"
:
2
,
"amount"
:
8
}
,
{
"quantity"
:
4
,
"amount"
:
4
}
,
{
"quantity"
:
8
,
"amount"
:
2
}
,
{
"quantity"
:
16
,
"amount"
:
1
}
]
,
"discountInSets"
:
1
,
"discountMaximum"
:
16
}
]
,
"campaignArticles"
:
[
{
"campaignArticleId"
:
10083
,
"costPrice"
:
5
,
"price"
:
10
,
"discountCode"
:
1
,
"discountQuantity"
:
5
,
"discountAmount"
:
10
,
"discountPercentage"
:
0
,
"discountInSets"
:
1
,
"discountMaximum"
:
20
,
"articleFamilyId"
:
0
,
"clubId"
:
5
}
,
{
"campaignArticleId"
:
100087
,
"costPrice"
:
20
,
"price"
:
30
,
"discountCode"
:
2
,
"discountQuantity"
:
1
,
"discountAmount"
:
5
,
"discountPercentage"
:
0
,
"discountInSets"
:
1
,
"discountMaximum"
:
20
,
"articleFamilyId"
:
0
,
"clubId"
:
5
}
]
}
]
}
Insert/update list of campaigns for specific store
Not supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The Id of the store
Request Body schema:
application/json
campaigns
required
Array of
objects
Array
campaignId
required
integer
[ 0 .. ******** ]
The ID of the campaign.
campaignStart
string
<date-time>
Start date-time of the campaign in ISO8601 format.
campaignStop
string
<date-time>
End date-time of the campaign in ISO8601 format.
campaignType
integer
[ 0 .. 99 ]
Used for prioritizing campaigns. By default, the campaign that gets the customer the best deal is prioritized. Also used for summing up discounts per campaignType in built-in revenue analyses.
Any change of prioritization scheme from the default must be set up. Ask your Fiftytwo consultant.
description
string
Description of the campaign.
storeGroup
string
^$|^[^aA].{0,3}$
Controls distribution of the campaign to a group of stores or single stores when you use Enterprise service distribution. Store group maintenance is handled using the StoreGroup endpoint.
The store group ( which must match the regular expression ^$|^[^aA].{0,3}$ ) limits the campaign to the stores in the store group. The default value blank "" means all stores. If the campain must only apply to a single store, you can use a single store ID in this field.
campaignFamilies
Array of
objects
You can add individual articles as well as discount familes (also known as article families, see the ArticleFamiles endpoint) to campaigns. This group of settings concerns discount familes and forms a list of discountfamilies and their discounts, requirements, etc. to include in the campaign.
You can add a discount family to a campaign in order to give the discount family a certain discount when it's used in the campaign, but the discount family itself typically also has its own discount defined. Which discount will customers then get? If multiple discounts apply, 52ViKING always attempts to make sure that customers get the best deal.
A discount family (created with articleFamily service) with same family ID as the campaign family ID must be present with off-campaign settings. That is, if the discount family is normally without discounts, it must be created without discounts.
campaignArticles
Array of
objects
You can add individual articles as well as discount familes to campaigns. This group of settings concerns individual articles and forms a list of articles and their discounts, requirements, etc. to include in the campaign.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/campaigns/v1/chain/{chain}/store/{store}
https://api.fiftytwo.com
/campaigns/v1/chain/{chain}/store/{store}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"campaigns"
:
[
{
"campaignId"
:
1123168
,
"campaignStart"
:
"2017-06-28T22:00:00+00:00"
,
"campaignStop"
:
"2027-06-29T21:59:59+00:00"
,
"campaignType"
:
1
,
"storeGroup"
:
"ABCD"
,
"description"
:
"Test campaign"
,
"campaignFamilies"
:
[
{
"campaignFamilyId"
:
123
,
"description"
:
"campaign family 123"
,
"campaignFamilyDiscount"
:
[
{
"quantity"
:
1
,
"amount"
:
16
}
,
{
"quantity"
:
2
,
"amount"
:
8
}
,
{
"quantity"
:
4
,
"amount"
:
4
}
,
{
"quantity"
:
8
,
"amount"
:
2
}
,
{
"quantity"
:
16
,
"amount"
:
1
}
]
,
"discountInSets"
:
1
,
"discountMaximum"
:
16
}
,
{
"campaignFamilyId"
:
124
,
"description"
:
"campaign family 124"
,
"campaignFamilyDiscount"
:
[
{
"quantity"
:
1
,
"amount"
:
16
}
,
{
"quantity"
:
2
,
"amount"
:
8
}
,
{
"quantity"
:
4
,
"amount"
:
4
}
,
{
"quantity"
:
8
,
"amount"
:
2
}
,
{
"quantity"
:
16
,
"amount"
:
1
}
]
,
"discountInSets"
:
1
,
"discountMaximum"
:
16
}
]
,
"campaignArticles"
:
[
{
"campaignArticleId"
:
10083
,
"costPrice"
:
5
,
"price"
:
10
,
"discountCode"
:
1
,
"discountQuantity"
:
5
,
"discountAmount"
:
10
,
"discountPercentage"
:
0
,
"discountInSets"
:
1
,
"discountMaximum"
:
20
,
"articleFamilyId"
:
0
,
"clubId"
:
5
}
,
{
"campaignArticleId"
:
100087
,
"costPrice"
:
20
,
"price"
:
30
,
"discountCode"
:
2
,
"discountQuantity"
:
1
,
"discountAmount"
:
5
,
"discountPercentage"
:
0
,
"discountInSets"
:
1
,
"discountMaximum"
:
20
,
"articleFamilyId"
:
0
,
"clubId"
:
5
}
]
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Insert/update list of campaigns for all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
Request Body schema:
application/json
campaigns
required
Array of
objects
Array
campaignId
required
integer
[ 0 .. ******** ]
The ID of the campaign.
campaignStart
string
<date-time>
Start date-time of the campaign in ISO8601 format.
campaignStop
string
<date-time>
End date-time of the campaign in ISO8601 format.
campaignType
integer
[ 0 .. 99 ]
Used for prioritizing campaigns. By default, the campaign that gets the customer the best deal is prioritized. Also used for summing up discounts per campaignType in built-in revenue analyses.
Any change of prioritization scheme from the default must be set up. Ask your Fiftytwo consultant.
description
string
Description of the campaign.
storeGroup
string
^$|^[^aA].{0,3}$
Controls distribution of the campaign to a group of stores or single stores when you use Enterprise service distribution. Store group maintenance is handled using the StoreGroup endpoint.
The store group ( which must match the regular expression ^$|^[^aA].{0,3}$ ) limits the campaign to the stores in the store group. The default value blank "" means all stores. If the campain must only apply to a single store, you can use a single store ID in this field.
campaignFamilies
Array of
objects
You can add individual articles as well as discount familes (also known as article families, see the ArticleFamiles endpoint) to campaigns. This group of settings concerns discount familes and forms a list of discountfamilies and their discounts, requirements, etc. to include in the campaign.
You can add a discount family to a campaign in order to give the discount family a certain discount when it's used in the campaign, but the discount family itself typically also has its own discount defined. Which discount will customers then get? If multiple discounts apply, 52ViKING always attempts to make sure that customers get the best deal.
A discount family (created with articleFamily service) with same family ID as the campaign family ID must be present with off-campaign settings. That is, if the discount family is normally without discounts, it must be created without discounts.
campaignArticles
Array of
objects
You can add individual articles as well as discount familes to campaigns. This group of settings concerns individual articles and forms a list of articles and their discounts, requirements, etc. to include in the campaign.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/campaigns/v1/chain/{chain}
https://api.fiftytwo.com
/campaigns/v1/chain/{chain}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"campaigns"
:
[
{
"campaignId"
:
1123168
,
"campaignStart"
:
"2017-06-28T22:00:00+00:00"
,
"campaignStop"
:
"2027-06-29T21:59:59+00:00"
,
"campaignType"
:
1
,
"storeGroup"
:
"ABCD"
,
"description"
:
"Test campaign"
,
"campaignFamilies"
:
[
{
"campaignFamilyId"
:
123
,
"description"
:
"campaign family 123"
,
"campaignFamilyDiscount"
:
[
{
"quantity"
:
1
,
"amount"
:
16
}
,
{
"quantity"
:
2
,
"amount"
:
8
}
,
{
"quantity"
:
4
,
"amount"
:
4
}
,
{
"quantity"
:
8
,
"amount"
:
2
}
,
{
"quantity"
:
16
,
"amount"
:
1
}
]
,
"discountInSets"
:
1
,
"discountMaximum"
:
16
}
,
{
"campaignFamilyId"
:
124
,
"description"
:
"campaign family 124"
,
"campaignFamilyDiscount"
:
[
{
"quantity"
:
1
,
"amount"
:
16
}
,
{
"quantity"
:
2
,
"amount"
:
8
}
,
{
"quantity"
:
4
,
"amount"
:
4
}
,
{
"quantity"
:
8
,
"amount"
:
2
}
,
{
"quantity"
:
16
,
"amount"
:
1
}
]
,
"discountInSets"
:
1
,
"discountMaximum"
:
16
}
]
,
"campaignArticles"
:
[
{
"campaignArticleId"
:
10083
,
"costPrice"
:
5
,
"price"
:
10
,
"discountCode"
:
1
,
"discountQuantity"
:
5
,
"discountAmount"
:
10
,
"discountPercentage"
:
0
,
"discountInSets"
:
1
,
"discountMaximum"
:
20
,
"articleFamilyId"
:
0
,
"clubId"
:
5
}
,
{
"campaignArticleId"
:
100087
,
"costPrice"
:
20
,
"price"
:
30
,
"discountCode"
:
2
,
"discountQuantity"
:
1
,
"discountAmount"
:
5
,
"discountPercentage"
:
0
,
"discountInSets"
:
1
,
"discountMaximum"
:
20
,
"articleFamilyId"
:
0
,
"clubId"
:
5
}
]
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete campaign from specific store
Not supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
campaignId
required
string
The ID of the campaign
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/campaigns/v1/chain/{chain}/store/{store}/{campaignId}
https://api.fiftytwo.com
/campaigns/v1/chain/{chain}/store/{store}/{campaignId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete campaign from all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
campaignId
required
string
The ID of the campaign
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/campaigns/v1/chain/{chain}/{campaignId}
https://api.fiftytwo.com
/campaigns/v1/chain/{chain}/{campaignId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Customers
The customer management feature in 52ViKING is primarily intended for account customers, that is customers who are granted a certain credit and then pay for their shopping via invoices.
You typically maintain customers with the 52ViKING enterprise service because customers' accounts are typically centralized entities that are shared between stores. That's why the customers/chain/{chain} endpoint is often the endpoint to use.
Get customer by ID, chain, and store
path Parameters
customerId
required
integer
<int64>
The ID of the customer to retrieve
chainId
required
integer
<int32>
The ID of the chain
storeId
required
integer
<int32>
The ID of the store
Responses
200
Success
Response Schema:
application/json
customers
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/customers/v1/chain/{chainId}/store/{storeId}/{customerId}
https://api.fiftytwo.com
/customers/v1/chain/{chainId}/store/{storeId}/{customerId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"customers"
:
[
{
"customerId"
:
**********
,
"companyName"
:
"Customer 1"
,
"address"
:
"Customer road  2"
,
"address2"
:
"3. door right hand"
,
"zipCode"
:
1234
,
"city"
:
"Cust"
,
"telephone"
:
"00299**********"
,
"email"
:
"<EMAIL>"
,
"eanPublic"
:
"**********123"
,
"att"
:
"Attention"
,
"customerType"
:
0
,
"note"
:
"Customernote"
,
"paymentTextCode"
:
99
,
"staffDiscountPercentage"
:
[
1000
,
1500
,
2000
,
2500
,
3000
,
3500
,
4000
,
4500
,
5000
]
,
"promptForProject"
:
1
,
"promptForRequisition"
:
1
,
"promptForNote"
:
1
,
"customerStatus"
:
0
,
"customerGroup"
:
"G1"
,
"creditLimit"
:
1000
,
"balance"
:
500.95
,
"extendedCreditLimit"
:
2000
,
"extendedCreditLimitEndDate"
:
"2018-12-10"
,
"blockingCode"
:
0
,
"projects"
:
[
{
"projectId"
:
1234
,
"description"
:
"project 1234"
,
"priceList"
:
**********12345680
,
"notes"
:
"Notes for the project - any length"
}
,
{
"projectId"
:
2345
,
"description"
:
"project 2345"
,
"priceList"
:
**********23456800
,
"notes"
:
"Notes for the project - any length"
}
]
,
"labels"
:
[
{
"labelId"
:
**********23
,
"description"
:
"Label for project **********23"
,
"closed"
:
1
,
"name"
:
"Gary Label 2"
,
"address"
:
"Open road 7"
,
"zipCode"
:
"CM27180"
,
"city"
:
"Label 1 town"
}
,
{
"labelId"
:
**********12
,
"description"
:
"Label for project **********12"
,
"closed"
:
0
,
"name"
:
"Rita Label2"
,
"address"
:
"Closed road 9"
,
"zipCode"
:
"CR3218"
,
"city"
:
"Label2 town"
}
]
,
"cards"
:
[
{
"card"
:
"*******************"
,
"blockingCode"
:
0
,
"cardHolderName"
:
"cardholder 1"
,
"idNumber"
:
**********
,
"salutation"
:
"Mrs."
}
,
{
"card"
:
"234567890**********"
,
"blockingCode"
:
0
,
"cardHolderName"
:
"cardholder 2"
,
"idNumber"
:
**********
,
"salutation"
:
"Mr."
}
]
}
]
}
Get customer by ID and chain
path Parameters
customerId
required
integer
<int64>
The ID of the customer to retrieve
chainId
required
integer
<int32>
The ID of the chain
Responses
200
Success
Response Schema:
application/json
customers
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/customers/v1/chain/{chainId}/{customerId}
https://api.fiftytwo.com
/customers/v1/chain/{chainId}/{customerId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"customers"
:
[
{
"customerId"
:
**********
,
"companyName"
:
"Customer 1"
,
"address"
:
"Customer road  2"
,
"address2"
:
"3. door right hand"
,
"zipCode"
:
1234
,
"city"
:
"Cust"
,
"telephone"
:
"00299**********"
,
"email"
:
"<EMAIL>"
,
"eanPublic"
:
"**********123"
,
"att"
:
"Attention"
,
"customerType"
:
0
,
"note"
:
"Customernote"
,
"paymentTextCode"
:
99
,
"staffDiscountPercentage"
:
[
1000
,
1500
,
2000
,
2500
,
3000
,
3500
,
4000
,
4500
,
5000
]
,
"promptForProject"
:
1
,
"promptForRequisition"
:
1
,
"promptForNote"
:
1
,
"customerStatus"
:
0
,
"customerGroup"
:
"G1"
,
"creditLimit"
:
1000
,
"balance"
:
500.95
,
"extendedCreditLimit"
:
2000
,
"extendedCreditLimitEndDate"
:
"2018-12-10"
,
"blockingCode"
:
0
,
"projects"
:
[
{
"projectId"
:
1234
,
"description"
:
"project 1234"
,
"priceList"
:
**********12345680
,
"notes"
:
"Notes for the project - any length"
}
,
{
"projectId"
:
2345
,
"description"
:
"project 2345"
,
"priceList"
:
**********23456800
,
"notes"
:
"Notes for the project - any length"
}
]
,
"labels"
:
[
{
"labelId"
:
**********23
,
"description"
:
"Label for project **********23"
,
"closed"
:
1
,
"name"
:
"Gary Label 2"
,
"address"
:
"Open road 7"
,
"zipCode"
:
"CM27180"
,
"city"
:
"Label 1 town"
}
,
{
"labelId"
:
**********12
,
"description"
:
"Label for project **********12"
,
"closed"
:
0
,
"name"
:
"Rita Label2"
,
"address"
:
"Closed road 9"
,
"zipCode"
:
"CR3218"
,
"city"
:
"Label2 town"
}
]
,
"cards"
:
[
{
"card"
:
"*******************"
,
"blockingCode"
:
0
,
"cardHolderName"
:
"cardholder 1"
,
"idNumber"
:
**********
,
"salutation"
:
"Mrs."
}
,
{
"card"
:
"234567890**********"
,
"blockingCode"
:
0
,
"cardHolderName"
:
"cardholder 2"
,
"idNumber"
:
**********
,
"salutation"
:
"Mr."
}
]
}
]
}
Insert/update list of customers for specific store
Not supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
Request Body schema:
application/json
customers
required
Array of
objects
Array
customerId
required
integer
[ 1 .. ********99 ]
The ID of the customer.
companyName
string
Customer company name, if any.
address
string
Customer address line 1
address2
string
Customer address line 2
zipCode
integer
[ 0 .. 9999 ]
Customer zipcode (postcode).
city
string
Customer city name
telephone
string
Customer telephone number
email
string
<email>
Customer e-mail address
eanPublic
string
Unique customer number for public sector administration purposes. This field must be used when customerType is = 7 (public sector institution).
att
string
Contact person, if any.
note
string
Information about the customer that can be shown on POS.
customerType
integer
[ 0 .. 15 ]
Defines customer type.
1 = Customer with a credit account
0 = Staff
8 = Club member
9 = Fiftytwo loyalty customer
10 = Fiftytwo customer level A
11 = Fiftytwo loyalty customer level B
12 = Fiftytwo loyalty customer level C
13 = Fiftytwo loyalty customer level D
14 = 52 Loyalty customer level E
15 = Loyalty customer level F
Customer types 2-7 may be used for specific purposes.
paymentTextCode
integer
[ 0 .. 99 ]
Code that refers to text records about terms of payment. Payment text records are maintained internally. Ask your Fitytwo consultant if you're in doubt
staffDiscountPercentage
Array of
integers
[ 1 .. 9 ] items
[ items
[ 0 .. 10000 ]
]
Percentage rates for staff discount groups 1 to 9. Specified in hundredths of percent (5% is specified as 500). This allows for individual staff-specific discounts.
clubs
Array of
integers
[ 1 .. 10 ] items
[ items
[ 0 .. 99 ]
]
Clubs that the customer is a member of. Used to trigger club-based discounts.
staffDiscountLimit
number
decimal places <= 2
[ 0 .. 999999.99 ]
Specifies the maximum purchase amount for which staff discount wil be given. The period of aggregation is set by Bpar.ppers.
staffDiscountDisabled
integer
Enum:
0
1
Specifies if staff discount disabled.
0 = No
1 = Yes
promptForProject
integer
Enum:
0
1
Specifies if shop assistants must be prompted to enter a project number when serving the customer.
0 = No
1 = Yes
If yes, the project number will be inserted in the sales transaction.
promptForRequisition
integer
Enum:
0
1
Specifies if shop assistants must be prompted to enter a requisition number when serving the customer.
0 = No
1 = Yes
If yes, the requisition number will be inserted in the sales transaction.
promptForNote
integer
Enum:
0
1
Specifies if shop assistants must be prompted to enter a note when serving the customer.
0 = No
1 = Yes
If yes, the note will be inserted in the sales transaction.
customerStatus
integer
[ 0 .. 9 ]
Defines if the customer is valid for completing purchases. If the value is not 0, the customer is not valid, but shop assistants can (if allowed by store parameters) accept the customer despite the customer status.
For debitors (that is where a debitor record exists), the status field in the debitor record overrules this field. Status descriptions are maintained internally.
customerGroup
string
A reference to a customer group that holds textual description and a price list for customers in that customer group
creditLimit
integer
[ 0 .. ******** ]
Credit limit for the customer. A value of 0 means no credit limit check during sales, that is the customer has unlimited credit.
balance
number
decimal places <= 2
[ -999999.99 .. 999999.99 ]
Ability to adjust the customer's balance if deposits have been made on the customer account in the backend system. A positive value means that the customer is in credit.
extendedCreditLimit
integer
[ 0 .. ******** ]
Temporarily extend the customer's credit limit.
extendedCreditLimitEndDate
string
<date>
The date when the extended credit limit expires. The extended credit limit is then automatically reset.
blockingCode
integer
[ 0 .. 6 ]
Controls if account sales for the customer are allowed. Blocking codes are maintained internally (ask your Fiftytwo consultant if in doubt). However, the following codes can be generated by the system during balance control from the POS:
4 = Account overdraft
7 = Account not found
8 = Offline to account server
9 = POS offline
projects
Array of
objects
List of the customer's projects, if any. Customer projects are registered on sales transactions for invoicing purposes. Projects may have individual price lists.
labels
Array of
objects
Upon completion of a customer sale, it's possible to select a label containing customer address information. The selected label will be added to the receipt.
cards
Array of
objects
List of the customer's cards. May contain multiple cards because the customer may have several employees who may each have their own individual card representing the customer.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/customers/v1/chain/{chain}/store/{store}
https://api.fiftytwo.com
/customers/v1/chain/{chain}/store/{store}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"customers"
:
[
{
"customerId"
:
**********
,
"companyName"
:
"Customer 1"
,
"address"
:
"Customer road  2"
,
"address2"
:
"3. door right hand"
,
"zipCode"
:
1234
,
"city"
:
"Cust"
,
"telephone"
:
"00299**********"
,
"email"
:
"<EMAIL>"
,
"eanPublic"
:
"**********123"
,
"att"
:
"Attention"
,
"customerType"
:
0
,
"note"
:
"Customernote"
,
"paymentTextCode"
:
99
,
"staffDiscountPercentage"
:
[
1000
,
1500
,
2000
,
2500
,
3000
,
3500
,
4000
,
4500
,
5000
]
,
"promptForProject"
:
1
,
"promptForRequisition"
:
1
,
"promptForNote"
:
1
,
"customerStatus"
:
0
,
"customerGroup"
:
"G1"
,
"creditLimit"
:
1000
,
"balance"
:
500.95
,
"extendedCreditLimit"
:
2000
,
"extendedCreditLimitEndDate"
:
"2018-12-10"
,
"blockingCode"
:
0
,
"projects"
:
[
{
"projectId"
:
1234
,
"description"
:
"project 1234"
,
"priceList"
:
**********12345680
,
"notes"
:
"Notes for the project - any length"
}
,
{
"projectId"
:
2345
,
"description"
:
"project 2345"
,
"priceList"
:
**********23456800
,
"notes"
:
"Notes for the project - any length"
}
]
,
"labels"
:
[
{
"labelId"
:
**********23
,
"description"
:
"Label for project **********23"
,
"closed"
:
1
,
"name"
:
"Gary Label 2"
,
"address"
:
"Open road 7"
,
"zipCode"
:
"CM27180"
,
"city"
:
"Label 1 town"
}
,
{
"labelId"
:
**********12
,
"description"
:
"Label for project **********12"
,
"closed"
:
0
,
"name"
:
"Rita Label2"
,
"address"
:
"Closed road 9"
,
"zipCode"
:
"CR3218"
,
"city"
:
"Label2 town"
}
]
,
"cards"
:
[
{
"card"
:
"*******************"
,
"blockingCode"
:
0
,
"cardHolderName"
:
"cardholder 1"
,
"idNumber"
:
**********
,
"salutation"
:
"Mrs."
}
,
{
"card"
:
"234567890**********"
,
"blockingCode"
:
0
,
"cardHolderName"
:
"cardholder 2"
,
"idNumber"
:
**********
,
"salutation"
:
"Mr."
}
]
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Insert/update list of customers for all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
Request Body schema:
application/json
customers
required
Array of
objects
Array
customerId
required
integer
[ 1 .. ********99 ]
The ID of the customer.
companyName
string
Customer company name, if any.
address
string
Customer address line 1
address2
string
Customer address line 2
zipCode
integer
[ 0 .. 9999 ]
Customer zipcode (postcode).
city
string
Customer city name
telephone
string
Customer telephone number
email
string
<email>
Customer e-mail address
eanPublic
string
Unique customer number for public sector administration purposes. This field must be used when customerType is = 7 (public sector institution).
att
string
Contact person, if any.
note
string
Information about the customer that can be shown on POS.
customerType
integer
[ 0 .. 15 ]
Defines customer type.
1 = Customer with a credit account
0 = Staff
8 = Club member
9 = Fiftytwo loyalty customer
10 = Fiftytwo customer level A
11 = Fiftytwo loyalty customer level B
12 = Fiftytwo loyalty customer level C
13 = Fiftytwo loyalty customer level D
14 = 52 Loyalty customer level E
15 = Loyalty customer level F
Customer types 2-7 may be used for specific purposes.
paymentTextCode
integer
[ 0 .. 99 ]
Code that refers to text records about terms of payment. Payment text records are maintained internally. Ask your Fitytwo consultant if you're in doubt
staffDiscountPercentage
Array of
integers
[ 1 .. 9 ] items
[ items
[ 0 .. 10000 ]
]
Percentage rates for staff discount groups 1 to 9. Specified in hundredths of percent (5% is specified as 500). This allows for individual staff-specific discounts.
clubs
Array of
integers
[ 1 .. 10 ] items
[ items
[ 0 .. 99 ]
]
Clubs that the customer is a member of. Used to trigger club-based discounts.
staffDiscountLimit
number
decimal places <= 2
[ 0 .. 999999.99 ]
Specifies the maximum purchase amount for which staff discount wil be given. The period of aggregation is set by Bpar.ppers.
staffDiscountDisabled
integer
Enum:
0
1
Specifies if staff discount disabled.
0 = No
1 = Yes
promptForProject
integer
Enum:
0
1
Specifies if shop assistants must be prompted to enter a project number when serving the customer.
0 = No
1 = Yes
If yes, the project number will be inserted in the sales transaction.
promptForRequisition
integer
Enum:
0
1
Specifies if shop assistants must be prompted to enter a requisition number when serving the customer.
0 = No
1 = Yes
If yes, the requisition number will be inserted in the sales transaction.
promptForNote
integer
Enum:
0
1
Specifies if shop assistants must be prompted to enter a note when serving the customer.
0 = No
1 = Yes
If yes, the note will be inserted in the sales transaction.
customerStatus
integer
[ 0 .. 9 ]
Defines if the customer is valid for completing purchases. If the value is not 0, the customer is not valid, but shop assistants can (if allowed by store parameters) accept the customer despite the customer status.
For debitors (that is where a debitor record exists), the status field in the debitor record overrules this field. Status descriptions are maintained internally.
customerGroup
string
A reference to a customer group that holds textual description and a price list for customers in that customer group
creditLimit
integer
[ 0 .. ******** ]
Credit limit for the customer. A value of 0 means no credit limit check during sales, that is the customer has unlimited credit.
balance
number
decimal places <= 2
[ -999999.99 .. 999999.99 ]
Ability to adjust the customer's balance if deposits have been made on the customer account in the backend system. A positive value means that the customer is in credit.
extendedCreditLimit
integer
[ 0 .. ******** ]
Temporarily extend the customer's credit limit.
extendedCreditLimitEndDate
string
<date>
The date when the extended credit limit expires. The extended credit limit is then automatically reset.
blockingCode
integer
[ 0 .. 6 ]
Controls if account sales for the customer are allowed. Blocking codes are maintained internally (ask your Fiftytwo consultant if in doubt). However, the following codes can be generated by the system during balance control from the POS:
4 = Account overdraft
7 = Account not found
8 = Offline to account server
9 = POS offline
projects
Array of
objects
List of the customer's projects, if any. Customer projects are registered on sales transactions for invoicing purposes. Projects may have individual price lists.
labels
Array of
objects
Upon completion of a customer sale, it's possible to select a label containing customer address information. The selected label will be added to the receipt.
cards
Array of
objects
List of the customer's cards. May contain multiple cards because the customer may have several employees who may each have their own individual card representing the customer.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/customers/v1/chain/{chain}
https://api.fiftytwo.com
/customers/v1/chain/{chain}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"customers"
:
[
{
"customerId"
:
**********
,
"companyName"
:
"Customer 1"
,
"address"
:
"Customer road  2"
,
"address2"
:
"3. door right hand"
,
"zipCode"
:
1234
,
"city"
:
"Cust"
,
"telephone"
:
"00299**********"
,
"email"
:
"<EMAIL>"
,
"eanPublic"
:
"**********123"
,
"att"
:
"Attention"
,
"customerType"
:
0
,
"note"
:
"Customernote"
,
"paymentTextCode"
:
99
,
"staffDiscountPercentage"
:
[
1000
,
1500
,
2000
,
2500
,
3000
,
3500
,
4000
,
4500
,
5000
]
,
"promptForProject"
:
1
,
"promptForRequisition"
:
1
,
"promptForNote"
:
1
,
"customerStatus"
:
0
,
"customerGroup"
:
"G1"
,
"creditLimit"
:
1000
,
"balance"
:
500.95
,
"extendedCreditLimit"
:
2000
,
"extendedCreditLimitEndDate"
:
"2018-12-10"
,
"blockingCode"
:
0
,
"projects"
:
[
{
"projectId"
:
1234
,
"description"
:
"project 1234"
,
"priceList"
:
**********12345680
,
"notes"
:
"Notes for the project - any length"
}
,
{
"projectId"
:
2345
,
"description"
:
"project 2345"
,
"priceList"
:
**********23456800
,
"notes"
:
"Notes for the project - any length"
}
]
,
"labels"
:
[
{
"labelId"
:
**********23
,
"description"
:
"Label for project **********23"
,
"closed"
:
1
,
"name"
:
"Gary Label 2"
,
"address"
:
"Open road 7"
,
"zipCode"
:
"CM27180"
,
"city"
:
"Label 1 town"
}
,
{
"labelId"
:
**********12
,
"description"
:
"Label for project **********12"
,
"closed"
:
0
,
"name"
:
"Rita Label2"
,
"address"
:
"Closed road 9"
,
"zipCode"
:
"CR3218"
,
"city"
:
"Label2 town"
}
]
,
"cards"
:
[
{
"card"
:
"*******************"
,
"blockingCode"
:
0
,
"cardHolderName"
:
"cardholder 1"
,
"idNumber"
:
**********
,
"salutation"
:
"Mrs."
}
,
{
"card"
:
"234567890**********"
,
"blockingCode"
:
0
,
"cardHolderName"
:
"cardholder 2"
,
"idNumber"
:
**********
,
"salutation"
:
"Mr."
}
]
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete customer from specific store
Not supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
customerId
required
string
The ID of the customer
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/customers/v1/chain/{chain}/store/{store}/{customerId}
https://api.fiftytwo.com
/customers/v1/chain/{chain}/store/{store}/{customerId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete customer from all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
customerId
required
string
The ID of the customer
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/customers/v1/chain/{chain}/{customerId}
https://api.fiftytwo.com
/customers/v1/chain/{chain}/{customerId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Departments
You primarily use departments for VAT and financial/statistical purposes. A department covers a number of related article groups. For example, if you set up a Dairy department, it can cover the article groups Cheese, Milk, and Yoghourt.
You can assign a VAT rate to a department, so that all articles that belong in article groups under the department will get that VAT rate. You can also assign a finance account to a department, so that your organization can run statistics on the department's sales.
Departments are part of the article hierarchy. The hierarchy is chain-store-department-articlegroup-article.
Get department by ID, chain, and store
path Parameters
departmentId
required
integer
<int64>
The ID of the department to retrieve
chainId
required
integer
<int32>
The ID of the chain
storeId
required
integer
<int32>
The ID of the store
Responses
200
Success
Response Schema:
application/json
departments
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/departments/v1/chain/{chainId}/store/{storeId}/{departmentId}
https://api.fiftytwo.com
/departments/v1/chain/{chainId}/store/{storeId}/{departmentId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"departments"
:
[
{
"departmentId"
:
10
,
"description"
:
"department 10"
,
"account"
:
"**********"
}
,
{
"departmentId"
:
11
,
"description"
:
"department 11"
,
"account"
:
"**********"
}
]
}
Get department by ID and chain
path Parameters
departmentId
required
integer
<int64>
The ID of the department to retrieve
chainId
required
integer
<int32>
The ID of the chain
Responses
200
Success
Response Schema:
application/json
departments
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/departments/v1/chain/{chainId}/{departmentId}
https://api.fiftytwo.com
/departments/v1/chain/{chainId}/{departmentId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"departments"
:
[
{
"departmentId"
:
10
,
"description"
:
"department 10"
,
"account"
:
"**********"
}
,
{
"departmentId"
:
11
,
"description"
:
"department 11"
,
"account"
:
"**********"
}
]
}
Insert/update list of departments for specific store
Not supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
Request Body schema:
application/json
departments
required
Array of
objects
Array
departmentId
required
integer
[ 1 .. 999999 ]
The ID of the department.
description
string
[ 0 .. 30 ] characters
Short text information about the department.
account
string
[ 0 .. 10 ] characters
Account to use for on accounting journals relating to the department.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/departments/v1/chain/{chain}/store/{store}
https://api.fiftytwo.com
/departments/v1/chain/{chain}/store/{store}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"departments"
:
[
{
"departmentId"
:
10
,
"description"
:
"department 10"
,
"account"
:
"**********"
}
,
{
"departmentId"
:
11
,
"description"
:
"department 11"
,
"account"
:
"**********"
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Insert/update list of departments for all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
Request Body schema:
application/json
departments
required
Array of
objects
Array
departmentId
required
integer
[ 1 .. 999999 ]
The ID of the department.
description
string
[ 0 .. 30 ] characters
Short text information about the department.
account
string
[ 0 .. 10 ] characters
Account to use for on accounting journals relating to the department.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/departments/v1/chain/{chain}
https://api.fiftytwo.com
/departments/v1/chain/{chain}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"departments"
:
[
{
"departmentId"
:
10
,
"description"
:
"department 10"
,
"account"
:
"**********"
}
,
{
"departmentId"
:
11
,
"description"
:
"department 11"
,
"account"
:
"**********"
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete departments from specific store
Not supported when master data is distributed by th 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
departmentId
required
integer
<int64>
The ID of the department
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/departments/v1/chain/{chain}/store/{store}/{departmentId}
https://api.fiftytwo.com
/departments/v1/chain/{chain}/store/{store}/{departmentId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete departments from all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
departmentsId
required
string
query Parameters
departmentId
integer
<int64>
The ID of the department
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/departments/v1/chain/{chain}/{departmentsId}
https://api.fiftytwo.com
/departments/v1/chain/{chain}/{departmentsId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
DiscountGroups
You use a discount group to group discounts with a number of articles. With discount groups you get a very high degree of flexibility when you set up your store's or organization's discounts.
When you've set up a discount group, you can add individual articles, discount families, article groups, articles from specific departments (such as fruit, veg, dairy, or similar), article lists, all of your articles, or any combination thereof to the discount group. You can even make exceptions, so that you can, for example, add all articles except articles X and Z.
With discount groups, you can grant the discounts themselves in many different ways, including discount ladders where quantity thresholds determine the size of the discounts. Plus, you can control discount groups by time with highly flexible options.
Example of a discount group that offers a time-controlled meal deal: Buy two free-range chicken sandwiches and an organic soft drink and save 30% on the total price of the articles on weekdays around lunchtime.
Get discount group by ID, chain, and store
path Parameters
discountGroupId
required
integer
<int64>
The ID of the discount group to retrieve
chainId
required
integer
<int32>
The ID of the chain
storeId
required
integer
<int32>
The ID of the store
Responses
200
Success
Response Schema:
application/json
discountGroups
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/discountgroups/v1/chain/{chainId}/store/{storeId}/{discountGroupId}
https://api.fiftytwo.com
/discountgroups/v1/chain/{chainId}/store/{storeId}/{discountGroupId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"discountGroups"
:
[
{
"discountGroupId"
:
90000
,
"text"
:
"discountamount"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2023-12-31T09:00:00"
,
"endTime"
:
"2026-12-31T09:00:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"code"
:
0
,
"type"
:
0
,
"trigger"
:
0
,
"action"
:
0
,
"discountSteps"
:
[
{
"quantity"
:
1
,
"value"
:
2
}
,
{
"quantity"
:
2
,
"value"
:
5
}
,
{
"quantity"
:
4
,
"value"
:
12
}
]
,
"maximum"
:
8
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
90002
,
"text"
:
"buy items get tokens"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-12-02T09:00:02"
,
"endTime"
:
"2026-12-02T09:00:02"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
2
,
"tokens"
:
{
"limit"
:
4
,
"tokenValue"
:
10
,
"tokenId"
:
90
,
"factor"
:
1
,
"confirm"
:
1
}
,
"quantity"
:
2
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
90011
,
"text"
:
"earn points for customers"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T03:30:09"
,
"endTime"
:
"2026-08-08T23:57:43"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"code"
:
0
,
"type"
:
0
,
"trigger"
:
1
,
"action"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
,
"quantity"
:
2
,
"numberOfPoints"
:
4
,
"pointType"
:
1
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
90012
,
"text"
:
"Loyalty : earn physical stamps"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-12-12T09:00:12"
,
"endTime"
:
"2026-12-12T09:00:12"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"code"
:
0
,
"type"
:
0
,
"trigger"
:
1
,
"action"
:
2
,
"quantity"
:
2
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
}
}
,
{
"discountGroupId"
:
90100
,
"text"
:
"Totalprice for items"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T09:01:00"
,
"endTime"
:
"2026-01-31T09:01:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
,
{
"id"
:
4
,
"exclude"
:
1
}
,
{
"id"
:
5
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
1
,
"trigger"
:
0
,
"action"
:
0
,
"discountSteps"
:
[
{
"quantity"
:
2
,
"value"
:
10
}
,
{
"quantity"
:
5
,
"value"
:
24
}
,
{
"quantity"
:
10
,
"value"
:
47
}
,
{
"quantity"
:
20
,
"value"
:
90
}
,
{
"quantity"
:
40
,
"value"
:
170
}
]
,
"maximum"
:
40
,
"discountInSets"
:
0
}
}
,
{
"discountGroupId"
:
90200
,
"text"
:
"% per item"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2023-12-31T09:02:00"
,
"endTime"
:
"2026-02-28T09:02:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
4
}
,
{
"id"
:
5
}
,
{
"id"
:
6
}
,
{
"id"
:
1
,
"exclude"
:
1
}
,
{
"id"
:
2
,
"exclude"
:
1
}
,
{
"id"
:
3
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
2
,
"trigger"
:
0
,
"action"
:
0
,
"discountSteps"
:
[
{
"quantity"
:
2
,
"value"
:
5
}
,
{
"quantity"
:
5
,
"value"
:
10
}
,
{
"quantity"
:
10
,
"value"
:
15
}
,
{
"quantity"
:
20
,
"value"
:
20
}
,
{
"quantity"
:
40
,
"value"
:
25
}
]
,
"maximum"
:
40
,
"discountInSets"
:
0
}
}
,
{
"discountGroupId"
:
90211
,
"text"
:
"earn bonus for customers"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T03:30:09"
,
"endTime"
:
"2026-08-08T23:57:43"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
,
{
"id"
:
4
,
"exclude"
:
1
}
,
{
"id"
:
5
,
"exclude"
:
1
}
,
{
"id"
:
6
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
2
,
"trigger"
:
1
,
"action"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
,
"numberOfPoints"
:
4
,
"pointType"
:
1
,
"maximum"
:
8
,
"multiUse"
:
1
,
"discountInSets"
:
1
,
"discountSteps"
:
[
{
"quantity"
:
1
,
"value"
:
2
}
,
{
"quantity"
:
4
,
"value"
:
3
}
]
}
}
,
{
"discountGroupId"
:
90300
,
"text"
:
"% to pay for cheapest item"
,
"receiptText"
:
"3 for 2 "
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-03-31T09:03:00"
,
"endTime"
:
"2026-03-31T09:03:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
51
}
,
{
"id"
:
52
}
,
{
"id"
:
53
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
3
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
3
,
"value"
:
0
}
}
,
{
"discountGroupId"
:
90400
,
"text"
:
"% to pay for expensive item"
,
"receiptText"
:
"3 for 2 "
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-04-30T09:04:00"
,
"endTime"
:
"2026-04-30T09:04:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
51
}
,
{
"id"
:
52
}
,
{
"id"
:
53
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
4
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
3
,
"value"
:
0
}
}
,
{
"discountGroupId"
:
90500
,
"text"
:
"Buy 5 and choose gift"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-05-31T09:05:00"
,
"endTime"
:
"2026-05-31T09:05:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
51
}
,
{
"id"
:
52
}
,
{
"id"
:
53
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
5
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
5
,
"maximum"
:
1
,
"giftList"
:
500
}
}
,
{
"discountGroupId"
:
91000
,
"text"
:
"Buy basket->money discount"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-02-28T09:20:00"
,
"endTime"
:
"2026-02-28T09:20:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
,
"quantity"
:
1
}
,
{
"id"
:
55
,
"quantity"
:
2
}
,
{
"id"
:
56
,
"quantity"
:
3
}
]
}
,
"conditions"
:
{
"type"
:
1
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
100
,
"maximum"
:
1
,
"value"
:
5
}
}
,
{
"discountGroupId"
:
91211
,
"text"
:
"Buy basket-> bonus"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T03:30:09"
,
"endTime"
:
"2026-08-08T23:57:43"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
,
"quantity"
:
1
}
,
{
"id"
:
55
,
"quantity"
:
2
}
,
{
"id"
:
56
,
"quantity"
:
3
}
]
}
,
"conditions"
:
{
"type"
:
1
,
"code"
:
2
,
"trigger"
:
1
,
"action"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
,
"value"
:
10
,
"maximum"
:
3
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
92000
,
"text"
:
"Buy for get monetary discount"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-02-28T09:20:00"
,
"endTime"
:
"2026-02-28T09:20:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"departments"
:
[
{
"id"
:
151
}
,
{
"id"
:
152
}
,
{
"id"
:
153
}
]
}
,
"conditions"
:
{
"type"
:
2
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
100
,
"maximum"
:
1
,
"value"
:
5
}
}
,
{
"discountGroupId"
:
92002
,
"text"
:
"buy for get tokens"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-12-02T09:20:02"
,
"endTime"
:
"2026-12-02T09:20:02"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articleGroups"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
,
{
"id"
:
14
,
"exclude"
:
1
}
,
{
"id"
:
15
,
"exclude"
:
1
}
,
{
"id"
:
16
,
"exclude"
:
1
}
]
,
"departments"
:
[
{
"id"
:
151
}
,
{
"id"
:
152
}
,
{
"id"
:
153
}
]
}
,
"conditions"
:
{
"type"
:
2
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
2
,
"tokens"
:
{
"limit"
:
4
,
"rounding"
:
1
,
"tokenValue"
:
10
,
"tokenId"
:
90
,
"factor"
:
1
,
"confirm"
:
1
}
,
"quantity"
:
2
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
92500
,
"text"
:
"Buy for to get gift"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-02-28T09:25:00"
,
"endTime"
:
"2026-02-28T09:25:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articleGroups"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
,
{
"id"
:
14
,
"exclude"
:
1
}
,
{
"id"
:
15
,
"exclude"
:
1
}
,
{
"id"
:
16
,
"exclude"
:
1
}
]
,
"departments"
:
[
{
"id"
:
151
}
,
{
"id"
:
152
}
,
{
"id"
:
153
}
]
}
,
"conditions"
:
{
"type"
:
2
,
"code"
:
5
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
500
,
"maximum"
:
1
,
"giftList"
:
500
}
}
]
}
Get discount group by ID and chain
path Parameters
discountGroupId
required
integer
<int64>
The ID of the discount group to retrieve
chainId
required
integer
<int32>
The ID of the chain
Responses
200
Success
Response Schema:
application/json
discountGroups
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/discountgroups/v1/chain/{chainId}/{discountGroupId}
https://api.fiftytwo.com
/discountgroups/v1/chain/{chainId}/{discountGroupId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"discountGroups"
:
[
{
"discountGroupId"
:
90000
,
"text"
:
"discountamount"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2023-12-31T09:00:00"
,
"endTime"
:
"2026-12-31T09:00:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"code"
:
0
,
"type"
:
0
,
"trigger"
:
0
,
"action"
:
0
,
"discountSteps"
:
[
{
"quantity"
:
1
,
"value"
:
2
}
,
{
"quantity"
:
2
,
"value"
:
5
}
,
{
"quantity"
:
4
,
"value"
:
12
}
]
,
"maximum"
:
8
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
90002
,
"text"
:
"buy items get tokens"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-12-02T09:00:02"
,
"endTime"
:
"2026-12-02T09:00:02"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
2
,
"tokens"
:
{
"limit"
:
4
,
"tokenValue"
:
10
,
"tokenId"
:
90
,
"factor"
:
1
,
"confirm"
:
1
}
,
"quantity"
:
2
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
90011
,
"text"
:
"earn points for customers"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T03:30:09"
,
"endTime"
:
"2026-08-08T23:57:43"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"code"
:
0
,
"type"
:
0
,
"trigger"
:
1
,
"action"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
,
"quantity"
:
2
,
"numberOfPoints"
:
4
,
"pointType"
:
1
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
90012
,
"text"
:
"Loyalty : earn physical stamps"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-12-12T09:00:12"
,
"endTime"
:
"2026-12-12T09:00:12"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"code"
:
0
,
"type"
:
0
,
"trigger"
:
1
,
"action"
:
2
,
"quantity"
:
2
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
}
}
,
{
"discountGroupId"
:
90100
,
"text"
:
"Totalprice for items"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T09:01:00"
,
"endTime"
:
"2026-01-31T09:01:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
,
{
"id"
:
4
,
"exclude"
:
1
}
,
{
"id"
:
5
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
1
,
"trigger"
:
0
,
"action"
:
0
,
"discountSteps"
:
[
{
"quantity"
:
2
,
"value"
:
10
}
,
{
"quantity"
:
5
,
"value"
:
24
}
,
{
"quantity"
:
10
,
"value"
:
47
}
,
{
"quantity"
:
20
,
"value"
:
90
}
,
{
"quantity"
:
40
,
"value"
:
170
}
]
,
"maximum"
:
40
,
"discountInSets"
:
0
}
}
,
{
"discountGroupId"
:
90200
,
"text"
:
"% per item"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2023-12-31T09:02:00"
,
"endTime"
:
"2026-02-28T09:02:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
4
}
,
{
"id"
:
5
}
,
{
"id"
:
6
}
,
{
"id"
:
1
,
"exclude"
:
1
}
,
{
"id"
:
2
,
"exclude"
:
1
}
,
{
"id"
:
3
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
2
,
"trigger"
:
0
,
"action"
:
0
,
"discountSteps"
:
[
{
"quantity"
:
2
,
"value"
:
5
}
,
{
"quantity"
:
5
,
"value"
:
10
}
,
{
"quantity"
:
10
,
"value"
:
15
}
,
{
"quantity"
:
20
,
"value"
:
20
}
,
{
"quantity"
:
40
,
"value"
:
25
}
]
,
"maximum"
:
40
,
"discountInSets"
:
0
}
}
,
{
"discountGroupId"
:
90211
,
"text"
:
"earn bonus for customers"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T03:30:09"
,
"endTime"
:
"2026-08-08T23:57:43"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
,
{
"id"
:
4
,
"exclude"
:
1
}
,
{
"id"
:
5
,
"exclude"
:
1
}
,
{
"id"
:
6
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
2
,
"trigger"
:
1
,
"action"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
,
"numberOfPoints"
:
4
,
"pointType"
:
1
,
"maximum"
:
8
,
"multiUse"
:
1
,
"discountInSets"
:
1
,
"discountSteps"
:
[
{
"quantity"
:
1
,
"value"
:
2
}
,
{
"quantity"
:
4
,
"value"
:
3
}
]
}
}
,
{
"discountGroupId"
:
90300
,
"text"
:
"% to pay for cheapest item"
,
"receiptText"
:
"3 for 2 "
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-03-31T09:03:00"
,
"endTime"
:
"2026-03-31T09:03:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
51
}
,
{
"id"
:
52
}
,
{
"id"
:
53
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
3
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
3
,
"value"
:
0
}
}
,
{
"discountGroupId"
:
90400
,
"text"
:
"% to pay for expensive item"
,
"receiptText"
:
"3 for 2 "
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-04-30T09:04:00"
,
"endTime"
:
"2026-04-30T09:04:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
51
}
,
{
"id"
:
52
}
,
{
"id"
:
53
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
4
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
3
,
"value"
:
0
}
}
,
{
"discountGroupId"
:
90500
,
"text"
:
"Buy 5 and choose gift"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-05-31T09:05:00"
,
"endTime"
:
"2026-05-31T09:05:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
51
}
,
{
"id"
:
52
}
,
{
"id"
:
53
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
5
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
5
,
"maximum"
:
1
,
"giftList"
:
500
}
}
,
{
"discountGroupId"
:
91000
,
"text"
:
"Buy basket->money discount"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-02-28T09:20:00"
,
"endTime"
:
"2026-02-28T09:20:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
,
"quantity"
:
1
}
,
{
"id"
:
55
,
"quantity"
:
2
}
,
{
"id"
:
56
,
"quantity"
:
3
}
]
}
,
"conditions"
:
{
"type"
:
1
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
100
,
"maximum"
:
1
,
"value"
:
5
}
}
,
{
"discountGroupId"
:
91211
,
"text"
:
"Buy basket-> bonus"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T03:30:09"
,
"endTime"
:
"2026-08-08T23:57:43"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
,
"quantity"
:
1
}
,
{
"id"
:
55
,
"quantity"
:
2
}
,
{
"id"
:
56
,
"quantity"
:
3
}
]
}
,
"conditions"
:
{
"type"
:
1
,
"code"
:
2
,
"trigger"
:
1
,
"action"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
,
"value"
:
10
,
"maximum"
:
3
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
92000
,
"text"
:
"Buy for get monetary discount"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-02-28T09:20:00"
,
"endTime"
:
"2026-02-28T09:20:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"departments"
:
[
{
"id"
:
151
}
,
{
"id"
:
152
}
,
{
"id"
:
153
}
]
}
,
"conditions"
:
{
"type"
:
2
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
100
,
"maximum"
:
1
,
"value"
:
5
}
}
,
{
"discountGroupId"
:
92002
,
"text"
:
"buy for get tokens"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-12-02T09:20:02"
,
"endTime"
:
"2026-12-02T09:20:02"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articleGroups"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
,
{
"id"
:
14
,
"exclude"
:
1
}
,
{
"id"
:
15
,
"exclude"
:
1
}
,
{
"id"
:
16
,
"exclude"
:
1
}
]
,
"departments"
:
[
{
"id"
:
151
}
,
{
"id"
:
152
}
,
{
"id"
:
153
}
]
}
,
"conditions"
:
{
"type"
:
2
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
2
,
"tokens"
:
{
"limit"
:
4
,
"rounding"
:
1
,
"tokenValue"
:
10
,
"tokenId"
:
90
,
"factor"
:
1
,
"confirm"
:
1
}
,
"quantity"
:
2
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
92500
,
"text"
:
"Buy for to get gift"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-02-28T09:25:00"
,
"endTime"
:
"2026-02-28T09:25:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articleGroups"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
,
{
"id"
:
14
,
"exclude"
:
1
}
,
{
"id"
:
15
,
"exclude"
:
1
}
,
{
"id"
:
16
,
"exclude"
:
1
}
]
,
"departments"
:
[
{
"id"
:
151
}
,
{
"id"
:
152
}
,
{
"id"
:
153
}
]
}
,
"conditions"
:
{
"type"
:
2
,
"code"
:
5
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
500
,
"maximum"
:
1
,
"giftList"
:
500
}
}
]
}
Insert/update list of discount groups for specific store
Not supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
Request Body schema:
application/json
discountGroups
required
Array of
objects
Array
discountGroupId
required
integer
[ 1 .. ******** ]
Discount group ID that identifies the discount group.
text
string
[ 0 .. 30 ] characters
Short text that describes the discount group.
receiptText
string
[ 0 .. 20 ] characters
Discount text to show on receipts. If undefined, the text “Discount” is used.
storeGroup
string
^$|^[^aA].{0,3}$
If you use 52ViKING enterprise service distribution, you can use this setting to limit distribution of the discount to a group of stores or to single stores.
The default value blank "" means all stores, but you can limit the campaign to a group of stores that matches the regular expression ^$|^[^aA].{0,3}$. You can also use a single store ID.
additionalTextSegment
integer
[ 1000 .. 9999 ]
Additional text segments are used to explain or elaborate on specific discounts or for configure printing of, for example, comeback vouchers. Specify the four-digit number of the required additional text section defined by your organization's Fiftytwo consultant.
control
object
dateTimeSettings
object
conditions
object
buySide
object
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/discountgroups/v1/chain/{chain}/store/{store}
https://api.fiftytwo.com
/discountgroups/v1/chain/{chain}/store/{store}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"discountGroups"
:
[
{
"discountGroupId"
:
90000
,
"text"
:
"discountamount"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2023-12-31T09:00:00"
,
"endTime"
:
"2026-12-31T09:00:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"code"
:
0
,
"type"
:
0
,
"trigger"
:
0
,
"action"
:
0
,
"discountSteps"
:
[
{
"quantity"
:
1
,
"value"
:
2
}
,
{
"quantity"
:
2
,
"value"
:
5
}
,
{
"quantity"
:
4
,
"value"
:
12
}
]
,
"maximum"
:
8
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
90002
,
"text"
:
"buy items get tokens"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-12-02T09:00:02"
,
"endTime"
:
"2026-12-02T09:00:02"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
2
,
"tokens"
:
{
"limit"
:
4
,
"tokenValue"
:
10
,
"tokenId"
:
90
,
"factor"
:
1
,
"confirm"
:
1
}
,
"quantity"
:
2
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
90011
,
"text"
:
"earn points for customers"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T03:30:09"
,
"endTime"
:
"2026-08-08T23:57:43"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"code"
:
0
,
"type"
:
0
,
"trigger"
:
1
,
"action"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
,
"quantity"
:
2
,
"numberOfPoints"
:
4
,
"pointType"
:
1
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
90012
,
"text"
:
"Loyalty : earn physical stamps"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-12-12T09:00:12"
,
"endTime"
:
"2026-12-12T09:00:12"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"code"
:
0
,
"type"
:
0
,
"trigger"
:
1
,
"action"
:
2
,
"quantity"
:
2
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
}
}
,
{
"discountGroupId"
:
90100
,
"text"
:
"Totalprice for items"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T09:01:00"
,
"endTime"
:
"2026-01-31T09:01:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
,
{
"id"
:
4
,
"exclude"
:
1
}
,
{
"id"
:
5
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
1
,
"trigger"
:
0
,
"action"
:
0
,
"discountSteps"
:
[
{
"quantity"
:
2
,
"value"
:
10
}
,
{
"quantity"
:
5
,
"value"
:
24
}
,
{
"quantity"
:
10
,
"value"
:
47
}
,
{
"quantity"
:
20
,
"value"
:
90
}
,
{
"quantity"
:
40
,
"value"
:
170
}
]
,
"maximum"
:
40
,
"discountInSets"
:
0
}
}
,
{
"discountGroupId"
:
90200
,
"text"
:
"% per item"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2023-12-31T09:02:00"
,
"endTime"
:
"2026-02-28T09:02:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
4
}
,
{
"id"
:
5
}
,
{
"id"
:
6
}
,
{
"id"
:
1
,
"exclude"
:
1
}
,
{
"id"
:
2
,
"exclude"
:
1
}
,
{
"id"
:
3
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
2
,
"trigger"
:
0
,
"action"
:
0
,
"discountSteps"
:
[
{
"quantity"
:
2
,
"value"
:
5
}
,
{
"quantity"
:
5
,
"value"
:
10
}
,
{
"quantity"
:
10
,
"value"
:
15
}
,
{
"quantity"
:
20
,
"value"
:
20
}
,
{
"quantity"
:
40
,
"value"
:
25
}
]
,
"maximum"
:
40
,
"discountInSets"
:
0
}
}
,
{
"discountGroupId"
:
90211
,
"text"
:
"earn bonus for customers"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T03:30:09"
,
"endTime"
:
"2026-08-08T23:57:43"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
,
{
"id"
:
4
,
"exclude"
:
1
}
,
{
"id"
:
5
,
"exclude"
:
1
}
,
{
"id"
:
6
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
2
,
"trigger"
:
1
,
"action"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
,
"numberOfPoints"
:
4
,
"pointType"
:
1
,
"maximum"
:
8
,
"multiUse"
:
1
,
"discountInSets"
:
1
,
"discountSteps"
:
[
{
"quantity"
:
1
,
"value"
:
2
}
,
{
"quantity"
:
4
,
"value"
:
3
}
]
}
}
,
{
"discountGroupId"
:
90300
,
"text"
:
"% to pay for cheapest item"
,
"receiptText"
:
"3 for 2 "
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-03-31T09:03:00"
,
"endTime"
:
"2026-03-31T09:03:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
51
}
,
{
"id"
:
52
}
,
{
"id"
:
53
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
3
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
3
,
"value"
:
0
}
}
,
{
"discountGroupId"
:
90400
,
"text"
:
"% to pay for expensive item"
,
"receiptText"
:
"3 for 2 "
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-04-30T09:04:00"
,
"endTime"
:
"2026-04-30T09:04:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
51
}
,
{
"id"
:
52
}
,
{
"id"
:
53
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
4
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
3
,
"value"
:
0
}
}
,
{
"discountGroupId"
:
90500
,
"text"
:
"Buy 5 and choose gift"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-05-31T09:05:00"
,
"endTime"
:
"2026-05-31T09:05:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
51
}
,
{
"id"
:
52
}
,
{
"id"
:
53
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
5
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
5
,
"maximum"
:
1
,
"giftList"
:
500
}
}
,
{
"discountGroupId"
:
91000
,
"text"
:
"Buy basket->money discount"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-02-28T09:20:00"
,
"endTime"
:
"2026-02-28T09:20:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
,
"quantity"
:
1
}
,
{
"id"
:
55
,
"quantity"
:
2
}
,
{
"id"
:
56
,
"quantity"
:
3
}
]
}
,
"conditions"
:
{
"type"
:
1
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
100
,
"maximum"
:
1
,
"value"
:
5
}
}
,
{
"discountGroupId"
:
91211
,
"text"
:
"Buy basket-> bonus"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T03:30:09"
,
"endTime"
:
"2026-08-08T23:57:43"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
,
"quantity"
:
1
}
,
{
"id"
:
55
,
"quantity"
:
2
}
,
{
"id"
:
56
,
"quantity"
:
3
}
]
}
,
"conditions"
:
{
"type"
:
1
,
"code"
:
2
,
"trigger"
:
1
,
"action"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
,
"value"
:
10
,
"maximum"
:
3
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
92000
,
"text"
:
"Buy for get monetary discount"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-02-28T09:20:00"
,
"endTime"
:
"2026-02-28T09:20:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"departments"
:
[
{
"id"
:
151
}
,
{
"id"
:
152
}
,
{
"id"
:
153
}
]
}
,
"conditions"
:
{
"type"
:
2
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
100
,
"maximum"
:
1
,
"value"
:
5
}
}
,
{
"discountGroupId"
:
92002
,
"text"
:
"buy for get tokens"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-12-02T09:20:02"
,
"endTime"
:
"2026-12-02T09:20:02"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articleGroups"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
,
{
"id"
:
14
,
"exclude"
:
1
}
,
{
"id"
:
15
,
"exclude"
:
1
}
,
{
"id"
:
16
,
"exclude"
:
1
}
]
,
"departments"
:
[
{
"id"
:
151
}
,
{
"id"
:
152
}
,
{
"id"
:
153
}
]
}
,
"conditions"
:
{
"type"
:
2
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
2
,
"tokens"
:
{
"limit"
:
4
,
"rounding"
:
1
,
"tokenValue"
:
10
,
"tokenId"
:
90
,
"factor"
:
1
,
"confirm"
:
1
}
,
"quantity"
:
2
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
92500
,
"text"
:
"Buy for to get gift"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-02-28T09:25:00"
,
"endTime"
:
"2026-02-28T09:25:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articleGroups"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
,
{
"id"
:
14
,
"exclude"
:
1
}
,
{
"id"
:
15
,
"exclude"
:
1
}
,
{
"id"
:
16
,
"exclude"
:
1
}
]
,
"departments"
:
[
{
"id"
:
151
}
,
{
"id"
:
152
}
,
{
"id"
:
153
}
]
}
,
"conditions"
:
{
"type"
:
2
,
"code"
:
5
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
500
,
"maximum"
:
1
,
"giftList"
:
500
}
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Insert/update list of discount groups for all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
Request Body schema:
application/json
discountGroups
required
Array of
objects
Array
discountGroupId
required
integer
[ 1 .. ******** ]
Discount group ID that identifies the discount group.
text
string
[ 0 .. 30 ] characters
Short text that describes the discount group.
receiptText
string
[ 0 .. 20 ] characters
Discount text to show on receipts. If undefined, the text “Discount” is used.
storeGroup
string
^$|^[^aA].{0,3}$
If you use 52ViKING enterprise service distribution, you can use this setting to limit distribution of the discount to a group of stores or to single stores.
The default value blank "" means all stores, but you can limit the campaign to a group of stores that matches the regular expression ^$|^[^aA].{0,3}$. You can also use a single store ID.
additionalTextSegment
integer
[ 1000 .. 9999 ]
Additional text segments are used to explain or elaborate on specific discounts or for configure printing of, for example, comeback vouchers. Specify the four-digit number of the required additional text section defined by your organization's Fiftytwo consultant.
control
object
dateTimeSettings
object
conditions
object
buySide
object
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/discountgroups/v1/chain/{chain}
https://api.fiftytwo.com
/discountgroups/v1/chain/{chain}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"discountGroups"
:
[
{
"discountGroupId"
:
90000
,
"text"
:
"discountamount"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2023-12-31T09:00:00"
,
"endTime"
:
"2026-12-31T09:00:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"code"
:
0
,
"type"
:
0
,
"trigger"
:
0
,
"action"
:
0
,
"discountSteps"
:
[
{
"quantity"
:
1
,
"value"
:
2
}
,
{
"quantity"
:
2
,
"value"
:
5
}
,
{
"quantity"
:
4
,
"value"
:
12
}
]
,
"maximum"
:
8
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
90002
,
"text"
:
"buy items get tokens"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-12-02T09:00:02"
,
"endTime"
:
"2026-12-02T09:00:02"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
2
,
"tokens"
:
{
"limit"
:
4
,
"tokenValue"
:
10
,
"tokenId"
:
90
,
"factor"
:
1
,
"confirm"
:
1
}
,
"quantity"
:
2
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
90011
,
"text"
:
"earn points for customers"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T03:30:09"
,
"endTime"
:
"2026-08-08T23:57:43"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"code"
:
0
,
"type"
:
0
,
"trigger"
:
1
,
"action"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
,
"quantity"
:
2
,
"numberOfPoints"
:
4
,
"pointType"
:
1
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
90012
,
"text"
:
"Loyalty : earn physical stamps"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-12-12T09:00:12"
,
"endTime"
:
"2026-12-12T09:00:12"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"code"
:
0
,
"type"
:
0
,
"trigger"
:
1
,
"action"
:
2
,
"quantity"
:
2
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
}
}
,
{
"discountGroupId"
:
90100
,
"text"
:
"Totalprice for items"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T09:01:00"
,
"endTime"
:
"2026-01-31T09:01:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
,
{
"id"
:
4
,
"exclude"
:
1
}
,
{
"id"
:
5
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
1
,
"trigger"
:
0
,
"action"
:
0
,
"discountSteps"
:
[
{
"quantity"
:
2
,
"value"
:
10
}
,
{
"quantity"
:
5
,
"value"
:
24
}
,
{
"quantity"
:
10
,
"value"
:
47
}
,
{
"quantity"
:
20
,
"value"
:
90
}
,
{
"quantity"
:
40
,
"value"
:
170
}
]
,
"maximum"
:
40
,
"discountInSets"
:
0
}
}
,
{
"discountGroupId"
:
90200
,
"text"
:
"% per item"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2023-12-31T09:02:00"
,
"endTime"
:
"2026-02-28T09:02:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
4
}
,
{
"id"
:
5
}
,
{
"id"
:
6
}
,
{
"id"
:
1
,
"exclude"
:
1
}
,
{
"id"
:
2
,
"exclude"
:
1
}
,
{
"id"
:
3
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
2
,
"trigger"
:
0
,
"action"
:
0
,
"discountSteps"
:
[
{
"quantity"
:
2
,
"value"
:
5
}
,
{
"quantity"
:
5
,
"value"
:
10
}
,
{
"quantity"
:
10
,
"value"
:
15
}
,
{
"quantity"
:
20
,
"value"
:
20
}
,
{
"quantity"
:
40
,
"value"
:
25
}
]
,
"maximum"
:
40
,
"discountInSets"
:
0
}
}
,
{
"discountGroupId"
:
90211
,
"text"
:
"earn bonus for customers"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T03:30:09"
,
"endTime"
:
"2026-08-08T23:57:43"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
1
}
,
{
"id"
:
2
}
,
{
"id"
:
3
}
,
{
"id"
:
4
,
"exclude"
:
1
}
,
{
"id"
:
5
,
"exclude"
:
1
}
,
{
"id"
:
6
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
2
,
"trigger"
:
1
,
"action"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
,
"numberOfPoints"
:
4
,
"pointType"
:
1
,
"maximum"
:
8
,
"multiUse"
:
1
,
"discountInSets"
:
1
,
"discountSteps"
:
[
{
"quantity"
:
1
,
"value"
:
2
}
,
{
"quantity"
:
4
,
"value"
:
3
}
]
}
}
,
{
"discountGroupId"
:
90300
,
"text"
:
"% to pay for cheapest item"
,
"receiptText"
:
"3 for 2 "
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-03-31T09:03:00"
,
"endTime"
:
"2026-03-31T09:03:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
51
}
,
{
"id"
:
52
}
,
{
"id"
:
53
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
3
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
3
,
"value"
:
0
}
}
,
{
"discountGroupId"
:
90400
,
"text"
:
"% to pay for expensive item"
,
"receiptText"
:
"3 for 2 "
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-04-30T09:04:00"
,
"endTime"
:
"2026-04-30T09:04:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
51
}
,
{
"id"
:
52
}
,
{
"id"
:
53
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
4
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
3
,
"value"
:
0
}
}
,
{
"discountGroupId"
:
90500
,
"text"
:
"Buy 5 and choose gift"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-05-31T09:05:00"
,
"endTime"
:
"2026-05-31T09:05:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"families"
:
[
{
"id"
:
51
}
,
{
"id"
:
52
}
,
{
"id"
:
53
}
]
}
,
"conditions"
:
{
"type"
:
0
,
"code"
:
5
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
5
,
"maximum"
:
1
,
"giftList"
:
500
}
}
,
{
"discountGroupId"
:
91000
,
"text"
:
"Buy basket->money discount"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-02-28T09:20:00"
,
"endTime"
:
"2026-02-28T09:20:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
,
"quantity"
:
1
}
,
{
"id"
:
55
,
"quantity"
:
2
}
,
{
"id"
:
56
,
"quantity"
:
3
}
]
}
,
"conditions"
:
{
"type"
:
1
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
100
,
"maximum"
:
1
,
"value"
:
5
}
}
,
{
"discountGroupId"
:
91211
,
"text"
:
"Buy basket-> bonus"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-01-31T03:30:09"
,
"endTime"
:
"2026-08-08T23:57:43"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
,
"quantity"
:
1
}
,
{
"id"
:
55
,
"quantity"
:
2
}
,
{
"id"
:
56
,
"quantity"
:
3
}
]
}
,
"conditions"
:
{
"type"
:
1
,
"code"
:
2
,
"trigger"
:
1
,
"action"
:
1
,
"customerGroup"
:
2
,
"customerType"
:
9
,
"value"
:
10
,
"maximum"
:
3
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
92000
,
"text"
:
"Buy for get monetary discount"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-02-28T09:20:00"
,
"endTime"
:
"2026-02-28T09:20:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articles"
:
[
{
"id"
:
54
}
,
{
"id"
:
55
}
,
{
"id"
:
56
}
,
{
"id"
:
51
,
"exclude"
:
1
}
,
{
"id"
:
52
,
"exclude"
:
1
}
,
{
"id"
:
53
,
"exclude"
:
1
}
]
,
"departments"
:
[
{
"id"
:
151
}
,
{
"id"
:
152
}
,
{
"id"
:
153
}
]
}
,
"conditions"
:
{
"type"
:
2
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
100
,
"maximum"
:
1
,
"value"
:
5
}
}
,
{
"discountGroupId"
:
92002
,
"text"
:
"buy for get tokens"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-12-02T09:20:02"
,
"endTime"
:
"2026-12-02T09:20:02"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articleGroups"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
,
{
"id"
:
14
,
"exclude"
:
1
}
,
{
"id"
:
15
,
"exclude"
:
1
}
,
{
"id"
:
16
,
"exclude"
:
1
}
]
,
"departments"
:
[
{
"id"
:
151
}
,
{
"id"
:
152
}
,
{
"id"
:
153
}
]
}
,
"conditions"
:
{
"type"
:
2
,
"code"
:
0
,
"trigger"
:
0
,
"action"
:
2
,
"tokens"
:
{
"limit"
:
4
,
"rounding"
:
1
,
"tokenValue"
:
10
,
"tokenId"
:
90
,
"factor"
:
1
,
"confirm"
:
1
}
,
"quantity"
:
2
,
"maximum"
:
1
,
"multiUse"
:
1
,
"discountInSets"
:
1
}
}
,
{
"discountGroupId"
:
92500
,
"text"
:
"Buy for to get gift"
,
"receiptText"
:
""
,
"storeGroup"
:
"GRP1"
,
"dateTimeSettings"
:
{
"startTime"
:
"2024-02-28T09:25:00"
,
"endTime"
:
"2026-02-28T09:25:00"
,
"allDays"
:
1
,
"allDay"
:
1
}
,
"buySide"
:
{
"articleGroups"
:
[
{
"id"
:
11
}
,
{
"id"
:
12
}
,
{
"id"
:
13
}
,
{
"id"
:
14
,
"exclude"
:
1
}
,
{
"id"
:
15
,
"exclude"
:
1
}
,
{
"id"
:
16
,
"exclude"
:
1
}
]
,
"departments"
:
[
{
"id"
:
151
}
,
{
"id"
:
152
}
,
{
"id"
:
153
}
]
}
,
"conditions"
:
{
"type"
:
2
,
"code"
:
5
,
"trigger"
:
0
,
"action"
:
0
,
"quantity"
:
500
,
"maximum"
:
1
,
"giftList"
:
500
}
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete discount groups from specific store
When you use this method on a 52ViKING enterprise service endpoint, only store-specific data is deleted.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
discountGroupId
required
integer
<int64>
The Id of the DiscountGroup
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/discountgroups/v1/chain/{chain}/store/{store}/{discountGroupId}
https://api.fiftytwo.com
/discountgroups/v1/chain/{chain}/store/{store}/{discountGroupId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete discount group from all stores
Only supported for 52ViKING enterprise service endpoints.
path Parameters
chain
required
integer
<int32>
The ID of the chain
discountGroupId
required
integer
<int64>
The ID of the discount group
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/discountgroups/v1/chain/{chain}/{discountGroupId}
https://api.fiftytwo.com
/discountgroups/v1/chain/{chain}/{discountGroupId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
SalesGroups
A sales group limits the quantity of articles within that sales group that can be sold in one customer purchase. If the limit is exceeded, shop assistants will get a notification, and no further sale of articles within the sales group will be possible during that purchase.
Example of articles that are often included in a sales group in order to limit the quantity that can be sold in a single purchase: Over-the-counter medicines.
Get sales group by ID, chain, and store
path Parameters
salesGroupId
required
integer
<int64>
The ID of the sales group to retrieve
chainId
required
integer
<int32>
The ID of the chain
storeId
required
integer
<int32>
The ID of the store
Responses
200
Success
Response Schema:
application/json
salesGroups
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/salesgroups/v1/chain/{chainId}/store/{storeId}/{salesGroupId}
https://api.fiftytwo.com
/salesgroups/v1/chain/{chainId}/store/{storeId}/{salesGroupId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"salesGroups"
:
[
{
"salesGroupId"
:
10
,
"description"
:
"Salesgroup 10"
,
"maximumQuantity"
:
10
,
"textReference"
:
1234
}
,
{
"salesGroupId"
:
11
,
"description"
:
"Salesgroup 11"
,
"maximumQuantity"
:
11
,
"textReference"
:
4321
}
]
}
Get sales group by ID and chain
path Parameters
salesGroupId
required
integer
<int64>
The ID of the sales group to retrieve
chainId
required
integer
<int32>
The ID of the chain
Responses
200
Success
Response Schema:
application/json
salesGroups
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/salesgroups/v1/chain/{chainId}/{salesGroupId}
https://api.fiftytwo.com
/salesgroups/v1/chain/{chainId}/{salesGroupId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"salesGroups"
:
[
{
"salesGroupId"
:
10
,
"description"
:
"Salesgroup 10"
,
"maximumQuantity"
:
10
,
"textReference"
:
1234
}
,
{
"salesGroupId"
:
11
,
"description"
:
"Salesgroup 11"
,
"maximumQuantity"
:
11
,
"textReference"
:
4321
}
]
}
Insert/update list of sales groups for specific store
Not supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
Request Body schema:
application/json
salesGroups
required
Array of
objects
Array
salesGroupId
required
integer
[ 0 .. 999 ]
The ID of the sales group.
description
string
A description of the sales group.
maximumQuantity
integer
[ 0 .. 999 ]
The maximum quantity of articles in this sales group that can be sold in one purchase.
textReference
integer
[ 0 .. 9999 ]
A reference to a text segment that will be shown to shop assistants if maximumQuantity is exceeded. Text segments are maintained internally. Ask your Fiftytwo consultant if you're in doubt.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/salesgroups/v1/chain/{chain}/store/{store}
https://api.fiftytwo.com
/salesgroups/v1/chain/{chain}/store/{store}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"salesGroups"
:
[
{
"salesGroupId"
:
10
,
"description"
:
"Salesgroup 10"
,
"maximumQuantity"
:
10
,
"textReference"
:
1234
}
,
{
"salesGroupId"
:
11
,
"description"
:
"Salesgroup 11"
,
"maximumQuantity"
:
11
,
"textReference"
:
4321
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Insert/update list of sales groups for all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
Request Body schema:
application/json
salesGroups
required
Array of
objects
Array
salesGroupId
required
integer
[ 0 .. 999 ]
The ID of the sales group.
description
string
A description of the sales group.
maximumQuantity
integer
[ 0 .. 999 ]
The maximum quantity of articles in this sales group that can be sold in one purchase.
textReference
integer
[ 0 .. 9999 ]
A reference to a text segment that will be shown to shop assistants if maximumQuantity is exceeded. Text segments are maintained internally. Ask your Fiftytwo consultant if you're in doubt.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/salesgroups/v1/chain/{chain}
https://api.fiftytwo.com
/salesgroups/v1/chain/{chain}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"salesGroups"
:
[
{
"salesGroupId"
:
10
,
"description"
:
"Salesgroup 10"
,
"maximumQuantity"
:
10
,
"textReference"
:
1234
}
,
{
"salesGroupId"
:
11
,
"description"
:
"Salesgroup 11"
,
"maximumQuantity"
:
11
,
"textReference"
:
4321
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete sales group from specific store
Not supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
store
required
integer
<int32>
The ID of the store
salesGroupId
required
string
The ID of the sales group
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/salesgroups/v1/chain/{chain}/store/{store}/{salesGroupId}
https://api.fiftytwo.com
/salesgroups/v1/chain/{chain}/store/{store}/{salesGroupId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete sales group from all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
departmentsId
required
string
query Parameters
salesGroupId
string
The ID of the sales group
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/salesgroups/v1/chain/{chain}/{departmentsId}
https://api.fiftytwo.com
/salesgroups/v1/chain/{chain}/{departmentsId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
StoreGroups
If you use 52ViKING enterprise service distribution, store groups let you limit access (for example to a campaign or a discount group) to selected stores. When you place store IDs in a store group, you can reference that store group so that only stores in the referenced store group have access. A store group can contain a single or multiple stores.
Get store group by ID, chain, and store
path Parameters
storeGroupId
required
string
The ID of the store group to retrieve
chainId
required
integer
<int32>
The ID of the chain
storeId
required
integer
<int32>
The ID of the store
Responses
200
Success
Response Schema:
application/json
storeGroups
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/storegroups/v1/chain/{chainId}/store/{storeId}/{storeGroupId}
https://api.fiftytwo.com
/storegroups/v1/chain/{chainId}/store/{storeId}/{storeGroupId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"storeGroups"
:
[
{
"storeGroupId"
:
"BBBB"
,
"description"
:
"My storegroup."
,
"storeIds"
:
[
"1"
,
"3..5"
,
"7"
,
"10"
]
}
]
}
Get store group by ID and chain
path Parameters
storeGroupId
required
string
The ID of the store group to retrieve
chainId
required
integer
<int32>
The ID of the chain
Responses
200
Success
Response Schema:
application/json
storeGroups
required
Array of
objects
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/storegroups/v1/chain/{chainId}/{storeGroupId}
https://api.fiftytwo.com
/storegroups/v1/chain/{chainId}/{storeGroupId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"storeGroups"
:
[
{
"storeGroupId"
:
"BBBB"
,
"description"
:
"My storegroup."
,
"storeIds"
:
[
"1"
,
"3..5"
,
"7"
,
"10"
]
}
]
}
Insert/update a list of store groups on all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
Request Body schema:
application/json
storeGroups
required
Array of
objects
Array
storeGroupId
required
string
^[B-Z][A-Z,0-9]{0,3}$
The ID of the store group. Reference this ID to scope, for example, a campaign or prices to stores in the store group.
Must match the regular expression ^[B-Z][A-Z,0-9]{0,3}$
description
string
[ 0 .. 32 ] characters
A short description of the store group.
storeIds
Array of
strings
The store IDs of stores in this store group. To include ranges of store IDs, use the format
1 3..5 7 10
, where '3..5' means 'from and including 3 to and including 5'.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/storegroups/v1/chain/{chain}
https://api.fiftytwo.com
/storegroups/v1/chain/{chain}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"storeGroups"
:
[
{
"storeGroupId"
:
"BBBB"
,
"description"
:
"My storegroup."
,
"storeIds"
:
[
"1"
,
"3..5"
,
"7"
,
"10"
]
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete a store group from all stores
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
storeGroupId
required
string
The ID of the store group
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/storegroups/v1/chain/{chain}/{storeGroupId}
https://api.fiftytwo.com
/storegroups/v1/chain/{chain}/{storeGroupId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
BaseData
Get processing status for master data.
All POST methods that return 202 Accepted provide a location header that contains a URL that references this endpoint.
Note that master data packages that have the status description COMPLETE will be deleted after some time.
path Parameters
id
required
integer
<int64>
The ID of a master data package as returned by POST methods.
Responses
200
Success
Response Schema:
text/plain
application/json
text/json
text/plain
id
integer
<int64>
chain
integer or null
<int32>
store
integer or null
<int32>
contentType
string or null
bodyData
string or null
statusCode
integer
<int32>
(DataStatusCodes)
Enum:
0
1
9
statusDescription
string or null
errorMessage
string or null
404
Not Found
Response Schema:
text/plain
application/json
text/json
text/plain
string
get
/basedata/v1/id/{id}
https://api.fiftytwo.com
/basedata/v1/id/{id}
Response samples
200
404
Content type
text/plain
application/json
text/json
text/plain
No sample
Stores
Get store by ID and chain
path Parameters
storeId
required
integer
<int64>
The ID of the store to retrieve
chainId
required
integer
<int32>
The ID of the chain
Responses
200
Success
Response Schema:
application/json
stores
required
Array of
objects
List of stores in your chain (if you use 52ViKING enterprise service distribution).
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/stores/v1/chain/{chainId}/{storeId}
https://api.fiftytwo.com
/stores/v1/chain/{chainId}/{storeId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"stores"
:
[
{
"storeId"
:
1234
,
"internalStoreId"
:
"STOREID"
,
"email"
:
"<EMAIL>"
,
"telephone"
:
"+45 *********"
,
"ownerType"
:
1
,
"location"
:
{
"longitude"
:
-100
,
"latitude"
:
90
}
,
"storeName"
:
"Mystore"
,
"addressLine1"
:
"Store street number"
,
"addressLine2"
:
"Other adress information"
,
"addressLine3"
:
"Store city"
,
"zipCode"
:
1234
,
"openingHours"
:
[
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
]
,
"companyInformation"
:
{
"VATNumber"
:
"**********"
,
"bankAccount"
:
"2345-**********"
,
"IBAN"
:
"********"
,
"swift"
:
"543217"
}
}
]
}
Get store by ID and chain
Deprecated
path Parameters
storeId
required
integer
<int64>
The ID of the store to retrieve
chainId
required
integer
<int32>
The ID of the chain
Responses
200
Success
Response Schema:
application/json
stores
required
Array of
objects
List of stores in your chain (if you use 52ViKING enterprise service distribution).
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
404
Not Found
get
/stores/v1/chain/{chainId}/store/{storeId}
https://api.fiftytwo.com
/stores/v1/chain/{chainId}/store/{storeId}
Response samples
***********
Content type
application/json
Copy
Expand all
Collapse all
{
"stores"
:
[
{
"storeId"
:
1234
,
"internalStoreId"
:
"STOREID"
,
"email"
:
"<EMAIL>"
,
"telephone"
:
"+45 *********"
,
"ownerType"
:
1
,
"location"
:
{
"longitude"
:
-100
,
"latitude"
:
90
}
,
"storeName"
:
"Mystore"
,
"addressLine1"
:
"Store street number"
,
"addressLine2"
:
"Other adress information"
,
"addressLine3"
:
"Store city"
,
"zipCode"
:
1234
,
"openingHours"
:
[
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
]
,
"companyInformation"
:
{
"VATNumber"
:
"**********"
,
"bankAccount"
:
"2345-**********"
,
"IBAN"
:
"********"
,
"swift"
:
"543217"
}
}
]
}
Insert/update specific store
Not supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
store
required
integer
<int32>
Request Body schema:
application/json
stores
required
Array of
objects
List of stores in your chain (if you use 52ViKING enterprise service distribution).
Array
storeId
required
integer
[ 1 .. 9999 ]
The ID of the store.
internalStoreId
string
[ 0 .. 10 ] characters
The ID of the store in your organization's ERP system.
storeName
string
[ 0 .. 30 ] characters
Name of the store.
addressLine1
string
[ 0 .. 30 ] characters
Address line 2.
addressLine2
string
[ 0 .. 30 ] characters
Address line 3.
addressLine3
string
[ 0 .. 30 ] characters
Address line 4.
zipCode
integer
[ 1 .. 99999 ]
Zip code (postcode).
email
string
<email>
E-mail address of the store.
telephone
string
[ 0 .. 16 ] characters
Telephone number of the store.
ownerType
integer
Store owner type. In organizations where different types of owners exist, you can use ownership to filter stores
0 = Privately owned
1 = Chain-owned
2 = Franchise
location
object
GPS coordinates of the store. This information is currently purely informational and has no impact on store functions.
openingHours
Array of
objects
= 7 items
Opening hours from Monday to Sunday. This information is purely informational and has no impact on store functions. Format is free.
companyInformation
object
Information about the company that owns the store.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/stores/v1/chain/{chain}/store/{store}
https://api.fiftytwo.com
/stores/v1/chain/{chain}/store/{store}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"stores"
:
[
{
"storeId"
:
1234
,
"internalStoreId"
:
"STOREID"
,
"email"
:
"<EMAIL>"
,
"telephone"
:
"+45 *********"
,
"ownerType"
:
1
,
"location"
:
{
"longitude"
:
-100
,
"latitude"
:
90
}
,
"storeName"
:
"Mystore"
,
"addressLine1"
:
"Store street number"
,
"addressLine2"
:
"Other adress information"
,
"addressLine3"
:
"Store city"
,
"zipCode"
:
1234
,
"openingHours"
:
[
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
]
,
"companyInformation"
:
{
"VATNumber"
:
"**********"
,
"bankAccount"
:
"2345-**********"
,
"IBAN"
:
"********"
,
"swift"
:
"543217"
}
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Insert/update list of stores on 52ViKING enterprise service
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The ID of the chain
Request Body schema:
application/json
stores
required
Array of
objects
List of stores in your chain (if you use 52ViKING enterprise service distribution).
Array
storeId
required
integer
[ 1 .. 9999 ]
The ID of the store.
internalStoreId
string
[ 0 .. 10 ] characters
The ID of the store in your organization's ERP system.
storeName
string
[ 0 .. 30 ] characters
Name of the store.
addressLine1
string
[ 0 .. 30 ] characters
Address line 2.
addressLine2
string
[ 0 .. 30 ] characters
Address line 3.
addressLine3
string
[ 0 .. 30 ] characters
Address line 4.
zipCode
integer
[ 1 .. 99999 ]
Zip code (postcode).
email
string
<email>
E-mail address of the store.
telephone
string
[ 0 .. 16 ] characters
Telephone number of the store.
ownerType
integer
Store owner type. In organizations where different types of owners exist, you can use ownership to filter stores
0 = Privately owned
1 = Chain-owned
2 = Franchise
location
object
GPS coordinates of the store. This information is currently purely informational and has no impact on store functions.
openingHours
Array of
objects
= 7 items
Opening hours from Monday to Sunday. This information is purely informational and has no impact on store functions. Format is free.
companyInformation
object
Information about the company that owns the store.
Responses
202
Accepted
Response Headers
Location
any
A url to be used for querying the processing status of the request body. Format {url}/v{version}/basedata/id/{id}
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
post
/stores/v1/chain/{chain}
https://api.fiftytwo.com
/stores/v1/chain/{chain}
Request samples
Payload
Content type
application/json
Copy
Expand all
Collapse all
{
"stores"
:
[
{
"storeId"
:
1234
,
"internalStoreId"
:
"STOREID"
,
"email"
:
"<EMAIL>"
,
"telephone"
:
"+45 *********"
,
"ownerType"
:
1
,
"location"
:
{
"longitude"
:
-100
,
"latitude"
:
90
}
,
"storeName"
:
"Mystore"
,
"addressLine1"
:
"Store street number"
,
"addressLine2"
:
"Other adress information"
,
"addressLine3"
:
"Store city"
,
"zipCode"
:
1234
,
"openingHours"
:
[
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
,
{
"open"
:
"08:00:00"
,
"close"
:
"20:00:00"
}
]
,
"companyInformation"
:
{
"VATNumber"
:
"**********"
,
"bankAccount"
:
"2345-**********"
,
"IBAN"
:
"********"
,
"swift"
:
"543217"
}
}
]
}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample
Delete store from 52ViKING enterprise service
Only supported when master data is distributed by the 52ViKING enterprise service.
path Parameters
chain
required
integer
<int32>
The Id of the Chain
storeId
required
string
The Id of the Store
Responses
202
Accepted
400
Bad Request
Response Schema:
text/plain
application/json
text/json
text/plain
string
delete
/stores/v1/chain/{chain}/{storeId}
https://api.fiftytwo.com
/stores/v1/chain/{chain}/{storeId}
Response samples
400
Content type
text/plain
application/json
text/json
text/plain
No sample