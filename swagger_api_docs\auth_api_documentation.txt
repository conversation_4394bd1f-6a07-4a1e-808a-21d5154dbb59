ReDoc
Auth
post
/v1/token
post
/v1/tokenform
post
/v1/user
post
/v1/pos
post
/v1/store
post
/v1/refresh
API docs by Redocly
AuthAPI
(v1)
Download OpenAPI specification:
Download
Auth
/v1/token
Request Body schema:
application/json
clientId
required
string
non-empty
clientSecret
required
string
non-empty
audience
required
string
non-empty
Responses
200
Success
Response Schema:
application/json
access_token
string or null
refresh_token
string or null
expires_in
integer
<int32>
token_type
string or null
post
/v1/token
https://api.fiftytwo.com
/v1/token
Request samples
Payload
Content type
application/json
Copy
{
"clientId"
:
"string"
,
"clientSecret"
:
"string"
,
"audience"
:
"string"
}
Response samples
200
Content type
application/json
Copy
{
"access_token"
:
"string"
,
"refresh_token"
:
"string"
,
"expires_in"
:
0
,
"token_type"
:
"string"
}
/v1/tokenform
Request Body schema:
application/x-www-form-urlencoded
ClientId
required
string
ClientSecret
required
string
Audience
required
string
Responses
200
Success
Response Schema:
application/json
access_token
string or null
refresh_token
string or null
expires_in
integer
<int32>
token_type
string or null
post
/v1/tokenform
https://api.fiftytwo.com
/v1/tokenform
Response samples
200
Content type
application/json
Copy
{
"access_token"
:
"string"
,
"refresh_token"
:
"string"
,
"expires_in"
:
0
,
"token_type"
:
"string"
}
/v1/user
Request Body schema:
application/json
username
required
string
non-empty
password
required
string
non-empty
Responses
200
Success
Response Schema:
application/json
access_token
string or null
refresh_token
string or null
expires_in
integer
<int32>
token_type
string or null
401
Unauthorized
Response Schema:
application/json
type
string or null
title
string or null
status
integer or null
<int32>
detail
string or null
instance
string or null
property name*
additional property
any
429
Too Many Requests
Response Schema:
application/json
type
string or null
title
string or null
status
integer or null
<int32>
detail
string or null
instance
string or null
property name*
additional property
any
post
/v1/user
https://api.fiftytwo.com
/v1/user
Request samples
Payload
Content type
application/json
Copy
{
"username"
:
"string"
,
"password"
:
"string"
}
Response samples
200
401
429
Content type
application/json
Copy
{
"access_token"
:
"string"
,
"refresh_token"
:
"string"
,
"expires_in"
:
0
,
"token_type"
:
"string"
}
/v1/pos
Request Body schema:
application/json
username
required
string
non-empty
password
required
string
non-empty
activationKey
required
string
non-empty
amount
integer
<int32>
Responses
200
Success
Response Schema:
application/json
access_token
string or null
refresh_token
string or null
expires_in
integer
<int32>
token_type
string or null
401
Unauthorized
Response Schema:
application/json
type
string or null
title
string or null
status
integer or null
<int32>
detail
string or null
instance
string or null
property name*
additional property
any
post
/v1/pos
https://api.fiftytwo.com
/v1/pos
Request samples
Payload
Content type
application/json
Copy
{
"username"
:
"string"
,
"password"
:
"string"
,
"activationKey"
:
"string"
,
"amount"
:
0
}
Response samples
200
401
Content type
application/json
Copy
{
"access_token"
:
"string"
,
"refresh_token"
:
"string"
,
"expires_in"
:
0
,
"token_type"
:
"string"
}
/v1/store
Request Body schema:
application/json
username
required
string
non-empty
password
required
string
non-empty
activationKey
required
string
non-empty
amount
integer
<int32>
Responses
200
Success
Response Schema:
application/json
access_token
string or null
refresh_token
string or null
expires_in
integer
<int32>
token_type
string or null
401
Unauthorized
Response Schema:
application/json
type
string or null
title
string or null
status
integer or null
<int32>
detail
string or null
instance
string or null
property name*
additional property
any
post
/v1/store
https://api.fiftytwo.com
/v1/store
Request samples
Payload
Content type
application/json
Copy
{
"username"
:
"string"
,
"password"
:
"string"
,
"activationKey"
:
"string"
,
"amount"
:
0
}
Response samples
200
401
Content type
application/json
Copy
{
"access_token"
:
"string"
,
"refresh_token"
:
"string"
,
"expires_in"
:
0
,
"token_type"
:
"string"
}
/v1/refresh
Request Body schema:
application/json
text/json
application/*+json
application/json
accessToken
string or null
refreshToken
string or null
Responses
200
Success
post
/v1/refresh
https://api.fiftytwo.com
/v1/refresh
Request samples
Payload
Content type
application/json
text/json
application/*+json
application/json
Copy
{
"accessToken"
:
"string"
,
"refreshToken"
:
"string"
}