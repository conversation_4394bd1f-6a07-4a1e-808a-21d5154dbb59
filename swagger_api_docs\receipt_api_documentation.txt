ReDoc
EndOfDay reports from Fiftytwo
get
Retrieve an End Of Day report by specifying (ChainNo, StoreNo and SessionNo).

An End Of Day report contains aggregated information for a salesday which

is identified by a sessionid.
ReceiptManagement
post
/receiptmanagement/v{version}
Receipts from Fiftytwo
get
Retrieve list of receipts for store identified by (ChainNo, StoreNo).
get
Retrieve list of receipts for a chain identified by ChainNo).
Receipts to Fiftytwo
get
Retrieve status of receipt based on specified parameters
get
Retrieve status of receipt in REST-like format based on specified parameters
get
Retrieve status of receipt based on order ID
post
Save receipt as XML in payload
API docs by Redocly
Receipt API
(v1)
Download OpenAPI specification:
Download
52RETAIL:
<EMAIL>
The Receipt API is used to
post receipts from external systems like webshops.This enables customers to return goods in a physical store.Posting receipts to Fiftytwo enables one point of entry for receipts e.g using method below
get receipts produced by Fiftytwo (or posted via method above).
get end of day reports produced by Fiftytwo
EndOfDay reports from Fiftytwo
Retrieve an End Of Day report by specifying (ChainNo, StoreNo and SessionNo).

An End Of Day report contains aggregated information for a salesday which

is identified by a sessionid.
The report contains
turnover per articlegroup
Payments per paymenttype
Payments per cardtype
Account payments
Total Flow in - amount added to tills
Total Flow out - amount removed from tills
Bank transfers - local and foreign currency
System Discrepancy - difference between sales and payments
Drawer discrepancy - expected vs. actual amount in drawer
path Parameters
chainNo
required
integer
<int32>
The ChainNo
storeNo
required
integer
<int32>
The StoreNo
sessionNo
required
integer
<int32>
Method returns a list of reports starting from the sessionNo+1. The client should keep the last received sessionNo.
If no reports are found an empty array is returned.
version
required
string
query Parameters
limit
integer
<int32>
Default:
1
Return up to limit end of day reports.
Responses
200
Success
get
/endofday/v{version}/chain/{chainNo}/store/{storeNo}/session/{sessionNo}
https://api.fiftytwo.com
/endofday/v{version}/chain/{chainNo}/store/{storeNo}/session/{sessionNo}
Response samples
200
Content type
application/xml
Copy
<
EndOfDayReports
xmlns:
xsi
=
"
http://www.w3.org/2001/XMLSchema-instance
"
xmlns:
xsd
=
"
http://www.w3.org/2001/XMLSchema
"
>
<
EndOfDayReport
>
<
ChainNo
>
0
</
ChainNo
>
<
StoreNo
>
2203
</
StoreNo
>
<
SessionNo
>
2242
</
SessionNo
>
<
SessionDate
>
2021-05-30T00:00:00
</
SessionDate
>
<
Report
>
<
AccountingReport
>
<
ArticleGroupTurnover
>
<
ArticleGroup
id
=
"
000000
"
financeAccount
=
"
"
amount
=
"
110750.17
"
VATcode
=
"
1
"
VATPercent
=
"
25
"
descripttion
=
"
"
/>
<
ArticleGroup
id
=
"
000020
"
financeAccount
=
"
"
amount
=
"
1164
"
VATcode
=
"
0
"
VATPercent
=
"
0
"
descripttion
=
"
EMBALLAGE
"
/>
<
ArticleGroup
id
=
"
000030
"
financeAccount
=
"
"
amount
=
"
4147.5
"
VATcode
=
"
0
"
VATPercent
=
"
0
"
descripttion
=
"
DELI
"
/>
<
ArticleGroup
id
=
"
000040
"
financeAccount
=
"
"
amount
=
"
17796
"
VATcode
=
"
0
"
VATPercent
=
"
0
"
descripttion
=
"
FRUGT OG GRØNT
"
/>
<
ArticleGroup
id
=
"
000060
"
financeAccount
=
"
"
amount
=
"
7493.01
"
VATcode
=
"
0
"
VATPercent
=
"
0
"
descripttion
=
"
BAGER
"
/>
<
ArticleGroup
id
=
"
000080
"
financeAccount
=
"
"
amount
=
"
49087.6
"
VATcode
=
"
0
"
VATPercent
=
"
0
"
descripttion
=
"
KOLONIAL
"
/>
<
ArticleGroup
id
=
"
000083
"
financeAccount
=
"
"
amount
=
"
14807.45
"
VATcode
=
"
0
"
VATPercent
=
"
0
"
descripttion
=
"
KIOSK
"
/>
<
ArticleGroup
id
=
"
000090
"
financeAccount
=
"
"
amount
=
"
36241.21
"
VATcode
=
"
0
"
VATPercent
=
"
0
"
descripttion
=
"
SLAGTER
"
/>
<
ArticleGroup
id
=
"
000310
"
financeAccount
=
"
"
amount
=
"
966
"
VATcode
=
"
0
"
VATPercent
=
"
0
"
descripttion
=
"
TEXTIL
"
/>
<
ArticleGroup
id
=
"
000320
"
financeAccount
=
"
"
amount
=
"
568
"
VATcode
=
"
1
"
VATPercent
=
"
25
"
descripttion
=
"
PLANTER
"
/>
<
ArticleGroup
id
=
"
000321
"
financeAccount
=
"
"
amount
=
"
66
"
VATcode
=
"
1
"
VATPercent
=
"
25
"
descripttion
=
"
HUS
&amp;
HAVE
"
/>
<
ArticleGroup
id
=
"
000330
"
financeAccount
=
"
"
amount
=
"
1716
"
VATcode
=
"
1
"
VATPercent
=
"
25
"
descripttion
=
"
BOLIG
&amp;
FRITID
"
/>
<
ArticleGroup
id
=
"
000370
"
financeAccount
=
"
"
amount
=
"
16405.23
"
VATcode
=
"
0
"
VATPercent
=
"
0
"
descripttion
=
"
DIVERSE
"
/>
<
ArticleGroup
id
=
"
000390
"
financeAccount
=
"
"
amount
=
"
1722.2
"
VATcode
=
"
1
"
VATPercent
=
"
25
"
descripttion
=
"
ELEKTRONIK
"
/>
<
ArticleGroup
id
=
"
000901
"
financeAccount
=
"
"
amount
=
"
-7197.5
"
VATcode
=
"
0
"
VATPercent
=
"
0
"
descripttion
=
"
ANDET SALG
"
/>
</
ArticleGroupTurnover
>
<
TotalArticleTurnover
financeAccount
=
"
111111
"
amount
=
"
255732.87
"
/>
<
Services
/>
<
TotalServices
financeAccount
=
"
131313
"
amount
=
"
0
"
/>
<
GrandTotal
financeAccount
=
"
141414
"
amount
=
"
255732.87
"
/>
<
PaymentTypes
>
<
PaymentType
id
=
"
000
"
financeAccount
=
"
000000
"
amount
=
"
0
"
description
=
"
KONTANT
"
/>
<
PaymentType
id
=
"
041
"
financeAccount
=
"
000000
"
amount
=
"
26317.5
"
description
=
"
KONTANT CMS
"
/>
<
PaymentType
id
=
"
219
"
financeAccount
=
"
000000
"
amount
=
"
226429.41
"
description
=
"
DANKORT
"
/>
<
CardPayments
/>
<
PaymentType
id
=
"
302
"
financeAccount
=
"
000000
"
amount
=
"
1751.85
"
description
=
"
KØBEKORT
"
/>
<
Accounts
>
<
Account
Account
=
"
********
"
amount
=
"
190
"
Description
=
"
NOTA: 999-1
"
/>
<
Account
Account
=
"
0
"
amount
=
"
496
"
Description
=
"
NOTA: 2-2
"
/>
<
Account
Account
=
"
0
"
amount
=
"
-496
"
Description
=
"
NOTA: 10-8
"
/>
<
Account
Account
=
"
********
"
amount
=
"
188
"
Description
=
"
NOTA: 999-8
"
/>
<
Account
Account
=
"
********
"
amount
=
"
29
"
Description
=
"
NOTA: 889-1
"
/>
<
Account
Account
=
"
********
"
amount
=
"
29
"
Description
=
"
NOTA: 889-2
"
/>
<
Account
Account
=
"
********
"
amount
=
"
29
"
Description
=
"
NOTA: 889-3
"
/>
<
Account
Account
=
"
********
"
amount
=
"
29
"
Description
=
"
NOTA: 889-4
"
/>
<
Account
Account
=
"
0
"
amount
=
"
207.4
"
Description
=
"
NOTA: 2-36
"
/>
<
Account
Account
=
"
********
"
amount
=
"
306
"
Description
=
"
NOTA: 999-9
"
/>
<
Account
Account
=
"
0
"
amount
=
"
341.45
"
Description
=
"
NOTA: 2-218
"
/>
<
Account
Account
=
"
0
"
amount
=
"
403
"
Description
=
"
NOTA: 1-21
"
/>
</
Accounts
>
<
PaymentType
id
=
"
410
"
financeAccount
=
"
"
amount
=
"
500
"
description
=
"
GAVEKORT
"
/>
<
PaymentType
id
=
"
421
"
financeAccount
=
"
"
amount
=
"
60
"
description
=
"
VÆRDIBEVIS
"
/>
<
PaymentType
id
=
"
435
"
financeAccount
=
"
"
amount
=
"
841.2
"
description
=
"
GAVEKORT SG
"
/>
<
PaymentType
id
=
"
440
"
financeAccount
=
"
"
amount
=
"
10
"
description
=
"
TILGODEBEVIS
"
/>
<
PaymentType
id
=
"
500
"
financeAccount
=
"
000000
"
amount
=
"
173.5
"
description
=
"
FLASKEBON
"
/>
<
PaymentType
id
=
"
510
"
financeAccount
=
"
000000
"
amount
=
"
-9
"
description
=
"
Donation
"
/>
<
TotalPayments
financeAccount
=
"
555555
"
amount
=
"
256074.46
"
/>
<
Floatins
>
<
PaymentType
id
=
"
000
"
financeAccount
=
"
000000
"
amount
=
"
-34725.5
"
description
=
"
KONTANT
"
/>
<
PaymentType
id
=
"
041
"
financeAccount
=
"
000000
"
amount
=
"
-43910
"
description
=
"
KONTANT CMS
"
/>
</
Floatins
>
<
TotalFloat
financeAccount
=
"
666666
"
amount
=
"
-78635.5
"
/>
<
Floatouts
>
<
PaymentType
id
=
"
000
"
financeAccount
=
"
000000
"
amount
=
"
53285.5
"
description
=
"
KONTANT
"
/>
<
PaymentType
id
=
"
041
"
financeAccount
=
"
000000
"
amount
=
"
24960
"
description
=
"
KONTANT CMS
"
/>
</
Floatouts
>
<
TotalBanked
financeAccount
=
"
777777
"
amount
=
"
78245.5
"
/>
<
TenderRounding
financeAccount
=
"
999999
"
amount
=
"
-6.09
"
/>
</
PaymentTypes
>
<
TotalTendering
financeAccount
=
"
111111
"
amount
=
"
255678.37
"
/>
<
SystemDiscrepancy
financeAccount
=
"
121212
"
amount
=
"
54.5
"
/>
</
AccountingReport
>
</
Report
>
</
EndOfDayReport
>
</
EndOfDayReports
>
ReceiptManagement
/receiptmanagement/v{version}
path Parameters
version
required
string
Request Body schema:
application/json
text/json
application/*+json
application/xml
text/xml
application/*+xml
application/json
customerKey
string or null
Responses
200
Success
post
/receiptmanagement/v{version}
https://api.fiftytwo.com
/receiptmanagement/v{version}
Request samples
Payload
Content type
application/json
text/json
application/*+json
application/xml
text/xml
application/*+xml
application/json
Copy
{
"customerKey"
:
"string"
}
Receipts from Fiftytwo
Retrieve list of receipts for store identified by (ChainNo, StoreNo).
Pass the ID of the last received receipt in lastReceiptId because the method
returns a list of receipts following lastReceiptId\n
Note that the last ReceiptId is returned in the header as X-Last-ReceiptId
path Parameters
chainNo
required
integer
<int32>
Chain number
storeNo
required
integer
<int32>
Store number
lastReceiptId
required
integer
<int64>
ID of last received receipt. Method returns receipts with IDs starting with lastReceiptId+1.
version
required
string
query Parameters
limit
integer
<int32>
Default:
100
Limits how many receipts to return
Responses
200
Success
Response Schema:
application/json
any
get
/receipts/v{version}/chain/{chainNo}/store/{storeNo}/receiptid/{lastReceiptId}
https://api.fiftytwo.com
/receipts/v{version}/chain/{chainNo}/store/{storeNo}/receiptid/{lastReceiptId}
Response samples
200
Content type
application/json
Copy
"[\n  {\n    \"PosReceiptDocumentVersion\": \"v1\",\n    \"Header\": {\n      \"Session\": 2244,\n      \"StoreNo\": 2203,\n      \"ExternalStoreId\": \"butnr\",\n      \"TillNo\": 17,\n      \"ReceiptNo\": 51,\n      \"TimeStamp\": 1655798817,\n      \"TransactionTime\": \"2022-06-21T10:06:57+02:00\",\n      \"Cashier\": 6000,\n      \"SalesPerson\": 6000,\n      \"Training\": false,\n      \"ReceiptCancelled\": false,\n      \"ReceiptParked\": false,\n      \"RefundReceipt\": false,\n      \"ExchangeReceipt\": false\n    },\n    \"Transactions\": [\n      {\n        \"Type\": \"SalesTransaction\",\n        \"TypeId\": 1,\n        \"SubType\": 0,\n        \"Seqno\": 1,\n        \"TransactionTime\": \"2022-06-21T10:06:57+02:00\",\n        \"SalesType\": \"ItemSale\",\n        \"ArticleNumber\": \"11\",\n        \"ReceiptText\": \"Article 11.\",\n        \"Quantity\": 1,\n        \"UnitSalesPrice\": 11.00,\n        \"QtySalesPrice\": 11.00,\n        \"VatAmount\": 2.20,\n        \"LineNumber\": 1,\n        \"Cancel\": false,\n        \"Refund\": false,\n        \"Inquiry\": false,\n        \"PriceChange\": false,\n        \"ArticleGroup\": 10\n      },\n      {\n        \"Type\": \"DiscountTransaction\",\n        \"TypeId\": 6,\n        \"SubType\": 0,\n        \"Seqno\": 2,\n        \"TransactionTime\": \"2022-06-21T10:06:57+02:00\",\n        \"DiscountStatus\": \"Booked\",\n        \"ArticleNumber\": \"11\",\n        \"DiscountType\": 0,\n        \"DiscountReasonCode\": 0,\n        \"DiscountAmount\": -1.67,\n        \"VatAmount\": -0.33,\n        \"Reference\": 0,\n        \"CampaignId\": 0,\n        \"DiscountedLine\": 1\n      },\n      {\n        \"Type\": \"SalesTransaction\",\n        \"TypeId\": 1,\n        \"SubType\": 0,\n        \"Seqno\": 3,\n        \"TransactionTime\": \"2022-06-21T10:07:56+02:00\",\n        \"SalesType\": \"ItemSale\",\n        \"ArticleNumber\": \"11\",\n        \"ReceiptText\": \"Article 11.\",\n        \"Quantity\": 1,\n        \"UnitSalesPrice\": 11.00,\n        \"QtySalesPrice\": 11.00,\n        \"VatAmount\": 2.20,\n        \"LineNumber\": 3,\n        \"Cancel\": false,\n        \"Refund\": false,\n        \"Inquiry\": false,\n        \"PriceChange\": false,\n        \"ArticleGroup\": 10\n      },\n      {\n        \"Type\": \"DiscountTransaction\",\n        \"TypeId\": 6,\n        \"SubType\": 0,\n        \"Seqno\": 4,\n        \"TransactionTime\": \"2022-06-21T10:06:57+02:00\",\n        \"DiscountStatus\": \"Booked\",\n        \"ArticleNumber\": \"11\",\n        \"DiscountType\": 0,\n        \"DiscountReasonCode\": 0,\n        \"DiscountAmount\": -1.67,\n        \"VatAmount\": -0.33,\n        \"Reference\": 0,\n        \"CampaignId\": 0,\n        \"DiscountedLine\": 3\n      },\n      {\n        \"Type\": \"SalesTransaction\",\n        \"TypeId\": 1,\n        \"SubType\": 0,\n        \"Seqno\": 5,\n        \"TransactionTime\": \"2022-06-21T10:07:59+02:00\",\n        \"SalesType\": \"ItemSale\",\n        \"ArticleNumber\": \"11\",\n        \"ReceiptText\": \"Article 11.\",\n        \"Quantity\": 1,\n        \"UnitSalesPrice\": 11.00,\n        \"QtySalesPrice\": 11.00,\n        \"VatAmount\": 2.20,\n        \"LineNumber\": 5,\n        \"Cancel\": false,\n        \"Refund\": false,\n        \"Inquiry\": false,\n        \"PriceChange\": false,\n        \"ArticleGroup\": 10\n      },\n      {\n        \"Type\": \"DiscountTransaction\",\n        \"TypeId\": 6,\n        \"SubType\": 0,\n        \"Seqno\": 6,\n        \"TransactionTime\": \"2022-06-21T10:06:57+02:00\",\n        \"DiscountStatus\": \"Booked\",\n        \"ArticleNumber\": \"11\",\n        \"DiscountType\": 0,\n        \"DiscountReasonCode\": 0,\n        \"DiscountAmount\": -1.66,\n        \"VatAmount\": -0.33,\n        \"Reference\": 0,\n        \"CampaignId\": 0,\n        \"DiscountedLine\": 5\n      },\n      {\n        \"Type\": \"DiscountTransaction\",\n        \"TypeId\": 19,\n        \"SubType\": 0,\n        \"Seqno\": 7,\n        \"TransactionTime\": \"2022-06-21T10:06:57+02:00\",\n        \"DiscountStatus\": \"Printed\",\n        \"ArticleNumber\": \"11\",\n        \"DiscountType\": 0,\n        \"DiscountReasonCode\": 0,\n        \"DiscountAmount\": -5.00,\n        \"VatAmount\": -1.00,\n        \"Reference\": 0,\n        \"CampaignId\": 0,\n        \"DiscountedLine\": 5\n      },\n      {\n        \"Type\": \"PaymentTransaction\",\n        \"TypeId\": 23,\n        \"SubType\": 0,\n        \"Seqno\": 8,\n        \"TransactionType\": \"Payment\",\n        \"TenderType\": 0,\n        \"TenderText\": \"KONTANT\",\n        \"PaidAmount\": 28.00,\n        \"Currency\": \"DKK\",\n        \"ExchangeRate\": 0.000,\n        \"PaidCurrency\": 0.00,\n        \"CashBack\": 0.00,\n        \"Rounding\": 0.00,\n        \"TenderInfo\": 0,\n        \"Points\": 0,\n        \"CustomerId\": 0,\n        \"TenderRefNo\": 1,\n        \"CashManagement\": false,\n        \"ExtPaymentNum\": 1,\n        \"Customer\": {\n        }\n      },\n      {\n        \"Type\": \"TransactionMisc\",\n        \"TypeId\": 47,\n        \"SubType\": 0,\n        \"Seqno\": 9,\n        \"MiscDesc\": \"Staff\",\n        \"CustomerType\": 0,\n        \"Percent\": 0,\n        \"CardIssueNo\": 0,\n        \"DiscountId\": 0,\n        \"CustomerId\": 0,\n        \"DiscountBase\": 28.00,\n        \"ReceiptTotal\": 28.00\n      },\n      {\n        \"Type\": \"TransactionMisc\",\n        \"TypeId\": 64,\n        \"SubType\": 0,\n        \"Seqno\": 10,\n        \"MiscDesc\": \"Vat\",\n        \"VatCode\": 1,\n        \"VatValue\": 28.00,\n        \"VatRate\": 25.0,\n        \"ReceiptTotal\": 28.00\n      },\n      {\n        \"Type\": \"ReceiptTotal\",\n        \"TypeId\": 22,\n        \"SubType\": 0,\n        \"Seqno\": 11,\n        \"ReceiptTotal\": 28.00,\n        \"NumberArticles\": 3,\n        \"NumberPackages\": 3,\n        \"NumberCancelledArticles\": 0,\n        \"CancelAmount\": 0.00,\n        \"HandlingTime\": 69,\n        \"TenderTime\": 3\n      }\n    ]\n  }\n]\n"
Retrieve list of receipts for a chain identified by ChainNo).
Pass the ID of the last received receipt in lastReceiptId because the method
returns a list of receipts following lastReceiptId\n
Note that the last ReceiptId is returned in the header as X-Last-ReceiptId
path Parameters
chainNo
required
integer
<int32>
Chain number
lastReceiptId
required
integer
<int64>
ID of last received receipt. Method returns receipts with IDs starting with lastReceiptId+1.
version
required
string
query Parameters
limit
integer
<int32>
Default:
100
Limits how many receipts to return
Responses
200
Success
Response Schema:
application/json
any
get
/receipts/v{version}/chain/{chainNo}/receiptid/{lastReceiptId}
https://api.fiftytwo.com
/receipts/v{version}/chain/{chainNo}/receiptid/{lastReceiptId}
Response samples
200
Content type
application/json
Copy
"[\n  {\n    \"PosReceiptDocumentVersion\": \"v1\",\n    \"Header\": {\n      \"Session\": 2244,\n      \"StoreNo\": 2203,\n      \"ExternalStoreId\": \"butnr\",\n      \"TillNo\": 17,\n      \"ReceiptNo\": 51,\n      \"TimeStamp\": 1655798817,\n      \"TransactionTime\": \"2022-06-21T10:06:57+02:00\",\n      \"Cashier\": 6000,\n      \"SalesPerson\": 6000,\n      \"Training\": false,\n      \"ReceiptCancelled\": false,\n      \"ReceiptParked\": false,\n      \"RefundReceipt\": false,\n      \"ExchangeReceipt\": false\n    },\n    \"Transactions\": [\n      {\n        \"Type\": \"SalesTransaction\",\n        \"TypeId\": 1,\n        \"SubType\": 0,\n        \"Seqno\": 1,\n        \"TransactionTime\": \"2022-06-21T10:06:57+02:00\",\n        \"SalesType\": \"ItemSale\",\n        \"ArticleNumber\": \"11\",\n        \"ReceiptText\": \"Article 11.\",\n        \"Quantity\": 1,\n        \"UnitSalesPrice\": 11.00,\n        \"QtySalesPrice\": 11.00,\n        \"VatAmount\": 2.20,\n        \"LineNumber\": 1,\n        \"Cancel\": false,\n        \"Refund\": false,\n        \"Inquiry\": false,\n        \"PriceChange\": false,\n        \"ArticleGroup\": 10\n      },\n      {\n        \"Type\": \"DiscountTransaction\",\n        \"TypeId\": 6,\n        \"SubType\": 0,\n        \"Seqno\": 2,\n        \"TransactionTime\": \"2022-06-21T10:06:57+02:00\",\n        \"DiscountStatus\": \"Booked\",\n        \"ArticleNumber\": \"11\",\n        \"DiscountType\": 0,\n        \"DiscountReasonCode\": 0,\n        \"DiscountAmount\": -1.67,\n        \"VatAmount\": -0.33,\n        \"Reference\": 0,\n        \"CampaignId\": 0,\n        \"DiscountedLine\": 1\n      },\n      {\n        \"Type\": \"SalesTransaction\",\n        \"TypeId\": 1,\n        \"SubType\": 0,\n        \"Seqno\": 3,\n        \"TransactionTime\": \"2022-06-21T10:07:56+02:00\",\n        \"SalesType\": \"ItemSale\",\n        \"ArticleNumber\": \"11\",\n        \"ReceiptText\": \"Article 11.\",\n        \"Quantity\": 1,\n        \"UnitSalesPrice\": 11.00,\n        \"QtySalesPrice\": 11.00,\n        \"VatAmount\": 2.20,\n        \"LineNumber\": 3,\n        \"Cancel\": false,\n        \"Refund\": false,\n        \"Inquiry\": false,\n        \"PriceChange\": false,\n        \"ArticleGroup\": 10\n      },\n      {\n        \"Type\": \"DiscountTransaction\",\n        \"TypeId\": 6,\n        \"SubType\": 0,\n        \"Seqno\": 4,\n        \"TransactionTime\": \"2022-06-21T10:06:57+02:00\",\n        \"DiscountStatus\": \"Booked\",\n        \"ArticleNumber\": \"11\",\n        \"DiscountType\": 0,\n        \"DiscountReasonCode\": 0,\n        \"DiscountAmount\": -1.67,\n        \"VatAmount\": -0.33,\n        \"Reference\": 0,\n        \"CampaignId\": 0,\n        \"DiscountedLine\": 3\n      },\n      {\n        \"Type\": \"SalesTransaction\",\n        \"TypeId\": 1,\n        \"SubType\": 0,\n        \"Seqno\": 5,\n        \"TransactionTime\": \"2022-06-21T10:07:59+02:00\",\n        \"SalesType\": \"ItemSale\",\n        \"ArticleNumber\": \"11\",\n        \"ReceiptText\": \"Article 11.\",\n        \"Quantity\": 1,\n        \"UnitSalesPrice\": 11.00,\n        \"QtySalesPrice\": 11.00,\n        \"VatAmount\": 2.20,\n        \"LineNumber\": 5,\n        \"Cancel\": false,\n        \"Refund\": false,\n        \"Inquiry\": false,\n        \"PriceChange\": false,\n        \"ArticleGroup\": 10\n      },\n      {\n        \"Type\": \"DiscountTransaction\",\n        \"TypeId\": 6,\n        \"SubType\": 0,\n        \"Seqno\": 6,\n        \"TransactionTime\": \"2022-06-21T10:06:57+02:00\",\n        \"DiscountStatus\": \"Booked\",\n        \"ArticleNumber\": \"11\",\n        \"DiscountType\": 0,\n        \"DiscountReasonCode\": 0,\n        \"DiscountAmount\": -1.66,\n        \"VatAmount\": -0.33,\n        \"Reference\": 0,\n        \"CampaignId\": 0,\n        \"DiscountedLine\": 5\n      },\n      {\n        \"Type\": \"DiscountTransaction\",\n        \"TypeId\": 19,\n        \"SubType\": 0,\n        \"Seqno\": 7,\n        \"TransactionTime\": \"2022-06-21T10:06:57+02:00\",\n        \"DiscountStatus\": \"Printed\",\n        \"ArticleNumber\": \"11\",\n        \"DiscountType\": 0,\n        \"DiscountReasonCode\": 0,\n        \"DiscountAmount\": -5.00,\n        \"VatAmount\": -1.00,\n        \"Reference\": 0,\n        \"CampaignId\": 0,\n        \"DiscountedLine\": 5\n      },\n      {\n        \"Type\": \"PaymentTransaction\",\n        \"TypeId\": 23,\n        \"SubType\": 0,\n        \"Seqno\": 8,\n        \"TransactionType\": \"Payment\",\n        \"TenderType\": 0,\n        \"TenderText\": \"KONTANT\",\n        \"PaidAmount\": 28.00,\n        \"Currency\": \"DKK\",\n        \"ExchangeRate\": 0.000,\n        \"PaidCurrency\": 0.00,\n        \"CashBack\": 0.00,\n        \"Rounding\": 0.00,\n        \"TenderInfo\": 0,\n        \"Points\": 0,\n        \"CustomerId\": 0,\n        \"TenderRefNo\": 1,\n        \"CashManagement\": false,\n        \"ExtPaymentNum\": 1,\n        \"Customer\": {\n        }\n      },\n      {\n        \"Type\": \"TransactionMisc\",\n        \"TypeId\": 47,\n        \"SubType\": 0,\n        \"Seqno\": 9,\n        \"MiscDesc\": \"Staff\",\n        \"CustomerType\": 0,\n        \"Percent\": 0,\n        \"CardIssueNo\": 0,\n        \"DiscountId\": 0,\n        \"CustomerId\": 0,\n        \"DiscountBase\": 28.00,\n        \"ReceiptTotal\": 28.00\n      },\n      {\n        \"Type\": \"TransactionMisc\",\n        \"TypeId\": 64,\n        \"SubType\": 0,\n        \"Seqno\": 10,\n        \"MiscDesc\": \"Vat\",\n        \"VatCode\": 1,\n        \"VatValue\": 28.00,\n        \"VatRate\": 25.0,\n        \"ReceiptTotal\": 28.00\n      },\n      {\n        \"Type\": \"ReceiptTotal\",\n        \"TypeId\": 22,\n        \"SubType\": 0,\n        \"Seqno\": 11,\n        \"ReceiptTotal\": 28.00,\n        \"NumberArticles\": 3,\n        \"NumberPackages\": 3,\n        \"NumberCancelledArticles\": 0,\n        \"CancelAmount\": 0.00,\n        \"HandlingTime\": 69,\n        \"TenderTime\": 3\n      }\n    ]\n  }\n]\n"
Receipts to Fiftytwo
Retrieve status of receipt based on specified parameters
Either enter orderId or enter the other parameters.
path Parameters
version
required
string
query Parameters
chain
integer
<int32>
Chain number
store
integer
<int32>
Store number
terminal
integer
<int32>
Terminal number
receiptNo
integer
<int32>
Receipt number
receiptDate
string
<date-time>
Receipt time stamp in the format yyyy'-'MM'-'dd'T'HH':'mm':'ss
orderId
integer
<int32>
Optional order ID
Responses
200
Success
Response Schema:
text/plain
application/json
text/json
application/xml
text/xml
text/plain
chain
integer
<int32>
store
integer
<int32>
terminal
integer
<int32>
receiptNo
integer
<int32>
orderId
integer or null
<int32>
receiptDate
string
<date-time>
statusCode
integer
<int32>
(ReceiptStatusCode)
Enum:
0
1
2
9
statusDescription
string or null
400
Bad Request
Response Schema:
text/plain
application/json
text/json
application/xml
text/xml
text/plain
string
500
Server Error
Response Schema:
text/plain
application/json
text/json
application/xml
text/xml
text/plain
string
get
/receipts/v{version}/getreceiptstatus
https://api.fiftytwo.com
/receipts/v{version}/getreceiptstatus
Response samples
200
400
500
Content type
text/plain
application/json
text/json
application/xml
text/xml
text/plain
No sample
Retrieve status of receipt in REST-like format based on specified parameters
path Parameters
chain
required
integer
<int32>
Chain number
store
required
integer
<int32>
Store number
terminal
required
integer
<int32>
Terminal number
receiptNo
required
integer
<int32>
Receipt number
receiptDate
required
string
<date-time>
Receipt time stamp in the format yyyy'-'MM'-'dd'T'HH':'mm':'ss
version
required
string
Responses
200
Success
Response Schema:
text/plain
application/json
text/json
application/xml
text/xml
text/plain
chain
integer
<int32>
store
integer
<int32>
terminal
integer
<int32>
receiptNo
integer
<int32>
orderId
integer or null
<int32>
receiptDate
string
<date-time>
statusCode
integer
<int32>
(ReceiptStatusCode)
Enum:
0
1
2
9
statusDescription
string or null
400
Bad Request
Response Schema:
text/plain
application/json
text/json
application/xml
text/xml
text/plain
string
500
Server Error
Response Schema:
text/plain
application/json
text/json
application/xml
text/xml
text/plain
string
get
/receipts/v{version}/chain/{chain}/store/{store}/terminal/{terminal}/receiptno/{receiptno}/receiptdate/{receiptdate}/status
https://api.fiftytwo.com
/receipts/v{version}/chain/{chain}/store/{store}/terminal/{terminal}/receiptno/{receiptno}/receiptdate/{receiptdate}/status
Response samples
200
400
500
Content type
text/plain
application/json
text/json
application/xml
text/xml
text/plain
No sample
Retrieve status of receipt based on order ID
path Parameters
orderId
required
integer
<int32>
Order ID
version
required
string
Responses
200
Success
Response Schema:
text/plain
application/json
text/json
application/xml
text/xml
text/plain
chain
integer
<int32>
store
integer
<int32>
terminal
integer
<int32>
receiptNo
integer
<int32>
orderId
integer or null
<int32>
receiptDate
string
<date-time>
statusCode
integer
<int32>
(ReceiptStatusCode)
Enum:
0
1
2
9
statusDescription
string or null
400
Bad Request
Response Schema:
text/plain
application/json
text/json
application/xml
text/xml
text/plain
string
500
Server Error
Response Schema:
text/plain
application/json
text/json
application/xml
text/xml
text/plain
string
get
/receipts/v{version}/orderid/{orderid}/status
https://api.fiftytwo.com
/receipts/v{version}/orderid/{orderid}/status
Response samples
200
400
500
Content type
text/plain
application/json
text/json
application/xml
text/xml
text/plain
No sample
Save receipt as XML in payload
path Parameters
version
required
string
Request Body schema:
application/xml
Schema not provided
Responses
200
Success
Response Schema:
text/plain
application/json
text/json
application/xml
text/xml
text/plain
string
400
Bad Request
Response Schema:
text/plain
application/json
text/json
application/xml
text/xml
text/plain
string
500
Server Error
Response Schema:
text/plain
application/json
text/json
application/xml
text/xml
text/plain
string
post
/receipts/v{version}/savereceipt
https://api.fiftytwo.com
/receipts/v{version}/savereceipt
Response samples
200
400
500
Content type
text/plain
application/json
text/json
application/xml
text/xml
text/plain
No sample