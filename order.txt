Orders
API docs by Redocly
Order API (v1)
Download OpenAPI specification:Download
Orders
Returns the order request given an order id
PATH PARAMETERS
orderId
required
integer <int32>
Id of the order
Responses
200
Success
GET
/orders/v1/orderid/{orderId}
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Saves an order to the database. Order id will be generated if not found in the order request
REQUEST BODY SCHEMA: application/xml
Order request as text/xml
header
required
any
additional
any
customer
required
any
collect
required
any
customerregistered
any
coupons
object
details
required
object
Responses
200
Success
POST
/orders/v1
Request samples
Payload
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8"?>
<order>
 <header orderid="10"/>
</order>
Orders
API docs by Redocly
Order API (v1)
Download OpenAPI specification:Download
Orders
Returns the order request given an order id
PATH PARAMETERS
orderId
required
integer <int32>
Id of the order
Responses
200
Success
GET
/orders/v1/orderid/{orderId}
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Saves an order to the database. Order id will be generated if not found in the order request
REQUEST BODY SCHEMA: application/xml
Order request as text/xml
header
required
any
additional
any
customer
required
any
collect
required
any
customerregistered
any
coupons
object
details
required
object
Responses
200
Success
POST
/orders/v1
Request samples
Payload
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8"?>
<order>
 <header orderid="10"/>
</order>
Orders
API docs by Redocly
Order API (v1)
Download OpenAPI specification:Download
Orders
Returns the order request given an order id
PATH PARAMETERS
orderId
required
integer <int32>
Id of the order
Responses
200
Success
GET
/orders/v1/orderid/{orderId}
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Saves an order to the database. Order id will be generated if not found in the order request
REQUEST BODY SCHEMA: application/xml
Order request as text/xml
header
required
any
additional
any
customer
required
any
collect
required
any
customerregistered
any
coupons
object
details
required
object
Responses
200
Success
POST
/orders/v1
Request samples
Payload
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8"?>
<order>
 <header orderid="10"/>
</order>
Orders
API docs by Redocly
Order API (v1)
Download OpenAPI specification:Download
Orders
Returns the order request given an order id
PATH PARAMETERS
orderId
required
integer <int32>
Id of the order
Responses
200
Success
GET
/orders/v1/orderid/{orderId}
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Saves an order to the database. Order id will be generated if not found in the order request
REQUEST BODY SCHEMA: application/xml
Order request as text/xml
header
required
any
additional
any
customer
required
any
collect
required
any
customerregistered
any
coupons
object
details
required
object
Responses
200
Success
POST
/orders/v1
Request samples
Payload
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8"?>
<order>
 <header orderid="10"/>
</order>
Orders
API docs by Redocly
Orders
API docs by Redocly
Orders
Orders
Orders
Orders
API docs by Redocly
API docs by Redocly
Order API (v1)
Download OpenAPI specification:Download
Orders
Returns the order request given an order id
PATH PARAMETERS
orderId
required
integer <int32>
Id of the order
Responses
200
Success
GET
/orders/v1/orderid/{orderId}
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Saves an order to the database. Order id will be generated if not found in the order request
REQUEST BODY SCHEMA: application/xml
Order request as text/xml
header
required
any
additional
any
customer
required
any
collect
required
any
customerregistered
any
coupons
object
details
required
object
Responses
200
Success
POST
/orders/v1
Request samples
Payload
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8"?>
<order>
 <header orderid="10"/>
</order>
Order API (v1)
Download OpenAPI specification:Download
Order API (v1)
Download OpenAPI specification:Download
Order API (v1)
Download OpenAPI specification:Download
Order API (v1)
(v1)
Download OpenAPI specification:Download
Download
Orders
Orders
Orders
Orders
Returns the order request given an order id
PATH PARAMETERS
orderId
required
integer <int32>
Id of the order
Responses
200
Success
GET
/orders/v1/orderid/{orderId}
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Returns the order request given an order id
PATH PARAMETERS
orderId
required
integer <int32>
Id of the order
Responses
200
Success
GET
/orders/v1/orderid/{orderId}
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Returns the order request given an order id
PATH PARAMETERS
orderId
required
integer <int32>
Id of the order
Responses
200
Success
Returns the order request given an order id
PATH PARAMETERS
orderId
required
integer <int32>
Id of the order
PATH PARAMETERS
orderId
required
integer <int32>
Id of the order
orderId
required
integer <int32>
Id of the order
orderId
required
integer <int32>
Id of the order
orderId
required
orderId
required
integer <int32>
Id of the order
integer <int32>
Id of the order
integer <int32>
integer
<int32>
Id of the order
Id of the order
Id of the order
Responses
200
Success
Responses
200
Success
200
Success
200
Success
Success
GET
/orders/v1/orderid/{orderId}
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
GET
/orders/v1/orderid/{orderId}
GET
/orders/v1/orderid/{orderId}
GET
/orders/v1/orderid/{orderId}
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
200
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Content type
application/xml
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Copy
Copy
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
<?xml version="1.0" encoding="utf-8" ?>
<order>
<order
<
>
<header store="123" chain="1" orderid ="10"/>
<header
<
store
="123"
=
"
"
chain
="1"
=
"
"
orderid
="10"
=
"
"
/>
<additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
<additional
<
remark
="Example order "
=
"
"
channel
="1"
=
"
"
reference
="example   ref "
=
"
"
externalcustomerid
="externalid"
=
"
"
/>
<customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
<customer
<
phone
="+4510111213"
=
"
"
mail
="<EMAIL>"
=
"
"
zipcode
="5555"
=
"
"
/>
<collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
<collect
<
chain
="1"
=
"
"
store
="1"
=
"
"
date
="2018-01-02"
=
"
"
from
="10:00:00"
=
"
"
to
="01:00:00"
=
"
"
/>
<customerregistered id="1000000925" type="8" clublist="1,2,10"/>
<customerregistered
<
id
="1000000925"
=
"
"
type
="8"
=
"
"
clublist
="1,2,10"
=
"
"
/>
<coupons>
<coupons
<
>
<coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
<coupon
<
id
="42"
=
"
"
isunique
="1"
=
"
"
barcode
="0000000042"
=
"
"
discountamount
="2"
=
"
"
/>
</coupons>
</coupons
</
>
<details>
<details
<
>
<item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
<item
<
number
="5740500000548"
=
"
"
quantity
="1"
=
"
"
description
="NEUTRAL    SÆBE "
=
"
"
unitprice
="29.95"
=
"
"
totalprice
="29.95"
=
"
"
>
<vat percent="25.00" amount="5.99"/>
<vat
<
percent
="25.00"
=
"
"
amount
="5.99"
=
"
"
/>
<potentialdiscountgroups>
<potentialdiscountgroups
<
>
<discountgroup>
<discountgroup
<
>
</discountgroup>
</discountgroup
</
>
</potentialdiscountgroups>
</potentialdiscountgroups
</
>
</item>
</item
</
>
<item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
<item
<
number
="5710357000183"
=
"
"
quantity
="2"
=
"
"
description
="BLOMME    TOMATER "
=
"
"
unitprice
="14.95"
=
"
"
totalprice
="29.90"
=
"
"
>
<vat percent="25.00" amount="5.98"/>
<vat
<
percent
="25.00"
=
"
"
amount
="5.98"
=
"
"
/>
<discount discountgroup="56" discounttype="12" amount="12.50"/>
<discount
<
discountgroup
="56"
=
"
"
discounttype
="12"
=
"
"
amount
="12.50"
=
"
"
/>
<potentialdiscountgroups>
<potentialdiscountgroups
<
>
<discountgroup>
<discountgroup
<
>
</discountgroup>
</discountgroup
</
>
<discountgroup>
<discountgroup
<
>
</discountgroup>
</discountgroup
</
>
</potentialdiscountgroups>
</potentialdiscountgroups
</
>
</item>
</item
</
>
<item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
<item
<
number
="5740700301582"
=
"
"
description
="GULD TUBORG "
=
"
"
quantity
="3"
=
"
"
unitprice
="10.95"
=
"
"
totalprice
="32.85"
=
"
"
agerestricted
="18"
=
"
"
>
<vat percent="25.00" amount="6.57"/>
<vat
<
percent
="25.00"
=
"
"
amount
="6.57"
=
"
"
/>
<linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
<linkeditem
<
number
="7000"
=
"
"
description
="FLASKE PANT "
=
"
"
unitprice
="1.50"
=
"
"
totalprice
="4.50"
=
"
"
quantity
="2"
=
"
"
>
<vat percent="25.00" amount="1.12"/>
<vat
<
percent
="25.00"
=
"
"
amount
="1.12"
=
"
"
/>
</linkeditem>
</linkeditem
</
>
<discount discountgroup="1680" discounttype="0" amount="2.90"/>
<discount
<
discountgroup
="1680"
=
"
"
discounttype
="0"
=
"
"
amount
="2.90"
=
"
"
/>
</item>
</item
</
>
<item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
<item
<
number
="210137"
=
"
"
description
="ROASTBEEF"
=
"
"
quantity
="1.5"
=
"
"
unitprice
="145.00"
=
"
"
totalprice
="217.50"
=
"
"
>
<vat percent="25.00" amount="43.50"/>
<vat
<
percent
="25.00"
=
"
"
amount
="43.50"
=
"
"
/>
<potentialdiscountgroups>
<potentialdiscountgroups
<
>
<discountgroup>
<discountgroup
<
>
</discountgroup>
</discountgroup
</
>
</potentialdiscountgroups>
</potentialdiscountgroups
</
>
</item>
</item
</
>
</details>
</details
</
>
</order>
</order
</
>
Saves an order to the database. Order id will be generated if not found in the order request
REQUEST BODY SCHEMA: application/xml
Order request as text/xml
header
required
any
additional
any
customer
required
any
collect
required
any
customerregistered
any
coupons
object
details
required
object
Responses
200
Success
POST
/orders/v1
Request samples
Payload
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8"?>
<order>
 <header orderid="10"/>
</order>
Saves an order to the database. Order id will be generated if not found in the order request
REQUEST BODY SCHEMA: application/xml
Order request as text/xml
header
required
any
additional
any
customer
required
any
collect
required
any
customerregistered
any
coupons
object
details
required
object
Responses
200
Success
POST
/orders/v1
Request samples
Payload
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8"?>
<order>
 <header orderid="10"/>
</order>
Saves an order to the database. Order id will be generated if not found in the order request
REQUEST BODY SCHEMA: application/xml
Order request as text/xml
header
required
any
additional
any
customer
required
any
collect
required
any
customerregistered
any
coupons
object
details
required
object
Responses
200
Success
Saves an order to the database. Order id will be generated if not found in the order request
REQUEST BODY SCHEMA: application/xml
application/xml
Order request as text/xml
Order request as text/xml
header
required
any
additional
any
customer
required
any
collect
required
any
customerregistered
any
coupons
object
details
required
object
header
required
any
additional
any
customer
required
any
collect
required
any
customerregistered
any
coupons
object
details
required
object
header
required
any
header
required
header
required
any
any
any
any
additional
any
additional
additional
any
any
any
any
customer
required
any
customer
required
customer
required
any
any
any
any
collect
required
any
collect
required
collect
required
any
any
any
any
customerregistered
any
customerregistered
customerregistered
any
any
any
any
coupons
object
coupons
coupons
coupons
object
object
object
object
details
required
object
details
required
details
details
required
object
object
object
object
Responses
200
Success
Responses
200
Success
200
Success
200
Success
Success
POST
/orders/v1
Request samples
Payload
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8"?>
<order>
 <header orderid="10"/>
</order>
POST
/orders/v1
POST
/orders/v1
POST
/orders/v1
Request samples
Payload
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Request samples
Payload
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Payload
Payload
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Content type
application/xml
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
Copy
Copy
Copy
<?xml version="1.0" encoding="utf-8" ?>
<order>
 <header store="123" chain="1" orderid ="10"/>
 <additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
 <customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
 <collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
 <customerregistered id="1000000925" type="8" clublist="1,2,10"/>
 <coupons>
  <coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
 </coupons>
 <details>
  <item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
   <vat percent="25.00" amount="5.99"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
   <vat percent="25.00" amount="5.98"/>
   <discount discountgroup="56" discounttype="12" amount="12.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000000965</discountgroup>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
  <item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
   <vat percent="25.00" amount="6.57"/>
   <linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
    <vat percent="25.00" amount="1.12"/>
   </linkeditem>
   <discount discountgroup="1680" discounttype="0" amount="2.90"/>
  </item>
  <item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
   <vat percent="25.00" amount="43.50"/>
   <potentialdiscountgroups>
    <discountgroup>0000001200</discountgroup>
   </potentialdiscountgroups>
  </item>
 </details>
</order>
<?xml version="1.0" encoding="utf-8" ?>
<order>
<order
<
>
<header store="123" chain="1" orderid ="10"/>
<header
<
store
="123"
=
"
"
chain
="1"
=
"
"
orderid
="10"
=
"
"
/>
<additional remark="Example order " channel="1" reference="example   ref " externalcustomerid="externalid"/>
<additional
<
remark
="Example order "
=
"
"
channel
="1"
=
"
"
reference
="example   ref "
=
"
"
externalcustomerid
="externalid"
=
"
"
/>
<customer phone="+4510111213" mail="<EMAIL>" zipcode="5555"/>
<customer
<
phone
="+4510111213"
=
"
"
mail
="<EMAIL>"
=
"
"
zipcode
="5555"
=
"
"
/>
<collect chain="1" store="1" date="2018-01-02" from="10:00:00" to="01:00:00"/>
<collect
<
chain
="1"
=
"
"
store
="1"
=
"
"
date
="2018-01-02"
=
"
"
from
="10:00:00"
=
"
"
to
="01:00:00"
=
"
"
/>
<customerregistered id="1000000925" type="8" clublist="1,2,10"/>
<customerregistered
<
id
="1000000925"
=
"
"
type
="8"
=
"
"
clublist
="1,2,10"
=
"
"
/>
<coupons>
<coupons
<
>
<coupon id="42" isunique="1" barcode="0000000042" discountamount="2"/>
<coupon
<
id
="42"
=
"
"
isunique
="1"
=
"
"
barcode
="0000000042"
=
"
"
discountamount
="2"
=
"
"
/>
</coupons>
</coupons
</
>
<details>
<details
<
>
<item number="5740500000548" quantity="1" description="NEUTRAL    SÆBE " unitprice="29.95" totalprice="29.95">
<item
<
number
="5740500000548"
=
"
"
quantity
="1"
=
"
"
description
="NEUTRAL    SÆBE "
=
"
"
unitprice
="29.95"
=
"
"
totalprice
="29.95"
=
"
"
>
<vat percent="25.00" amount="5.99"/>
<vat
<
percent
="25.00"
=
"
"
amount
="5.99"
=
"
"
/>
<potentialdiscountgroups>
<potentialdiscountgroups
<
>
<discountgroup>
<discountgroup
<
>
</discountgroup>
</discountgroup
</
>
</potentialdiscountgroups>
</potentialdiscountgroups
</
>
</item>
</item
</
>
<item number="5710357000183" quantity="2" description="BLOMME    TOMATER " unitprice="14.95" totalprice="29.90">
<item
<
number
="5710357000183"
=
"
"
quantity
="2"
=
"
"
description
="BLOMME    TOMATER "
=
"
"
unitprice
="14.95"
=
"
"
totalprice
="29.90"
=
"
"
>
<vat percent="25.00" amount="5.98"/>
<vat
<
percent
="25.00"
=
"
"
amount
="5.98"
=
"
"
/>
<discount discountgroup="56" discounttype="12" amount="12.50"/>
<discount
<
discountgroup
="56"
=
"
"
discounttype
="12"
=
"
"
amount
="12.50"
=
"
"
/>
<potentialdiscountgroups>
<potentialdiscountgroups
<
>
<discountgroup>
<discountgroup
<
>
</discountgroup>
</discountgroup
</
>
<discountgroup>
<discountgroup
<
>
</discountgroup>
</discountgroup
</
>
</potentialdiscountgroups>
</potentialdiscountgroups
</
>
</item>
</item
</
>
<item number="5740700301582" description="GULD TUBORG " quantity="3" unitprice="10.95" totalprice="32.85" agerestricted="18">
<item
<
number
="5740700301582"
=
"
"
description
="GULD TUBORG "
=
"
"
quantity
="3"
=
"
"
unitprice
="10.95"
=
"
"
totalprice
="32.85"
=
"
"
agerestricted
="18"
=
"
"
>
<vat percent="25.00" amount="6.57"/>
<vat
<
percent
="25.00"
=
"
"
amount
="6.57"
=
"
"
/>
<linkeditem number="7000" description="FLASKE PANT " unitprice="1.50" totalprice="4.50" quantity="2">
<linkeditem
<
number
="7000"
=
"
"
description
="FLASKE PANT "
=
"
"
unitprice
="1.50"
=
"
"
totalprice
="4.50"
=
"
"
quantity
="2"
=
"
"
>
<vat percent="25.00" amount="1.12"/>
<vat
<
percent
="25.00"
=
"
"
amount
="1.12"
=
"
"
/>
</linkeditem>
</linkeditem
</
>
<discount discountgroup="1680" discounttype="0" amount="2.90"/>
<discount
<
discountgroup
="1680"
=
"
"
discounttype
="0"
=
"
"
amount
="2.90"
=
"
"
/>
</item>
</item
</
>
<item number="210137" description="ROASTBEEF" quantity="1.5" unitprice="145.00" totalprice="217.50">
<item
<
number
="210137"
=
"
"
description
="ROASTBEEF"
=
"
"
quantity
="1.5"
=
"
"
unitprice
="145.00"
=
"
"
totalprice
="217.50"
=
"
"
>
<vat percent="25.00" amount="43.50"/>
<vat
<
percent
="25.00"
=
"
"
amount
="43.50"
=
"
"
/>
<potentialdiscountgroups>
<potentialdiscountgroups
<
>
<discountgroup>
<discountgroup
<
>
</discountgroup>
</discountgroup
</
>
</potentialdiscountgroups>
</potentialdiscountgroups
</
>
</item>
</item
</
>
</details>
</details
</
>
</order>
</order
</
>
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8"?>
<order>
 <header orderid="10"/>
</order>
Response samples
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8"?>
<order>
 <header orderid="10"/>
</order>
200
200
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8"?>
<order>
 <header orderid="10"/>
</order>
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8"?>
<order>
 <header orderid="10"/>
</order>
Content type
application/xml
Content type
application/xml
Copy
<?xml version="1.0" encoding="utf-8"?>
<order>
 <header orderid="10"/>
</order>
Copy
<?xml version="1.0" encoding="utf-8"?>
<order>
 <header orderid="10"/>
</order>
Copy
Copy
Copy
<?xml version="1.0" encoding="utf-8"?>
<order>
 <header orderid="10"/>
</order>
<?xml version="1.0" encoding="utf-8"?>
<order>
<order
<
>
<header orderid="10"/>
<header
<
orderid
="10"
=
"
"
/>
</order>
</order
</
>
