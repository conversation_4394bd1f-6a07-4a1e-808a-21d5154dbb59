from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import time
import os
from bs4 import BeautifulSoup

# Create output directory if it doesn't exist
output_dir = "swagger_api_docs"
os.makedirs(output_dir, exist_ok=True)
print(f"Created output directory: {output_dir}")

# Set up Chrome options
options = Options()
options.add_argument("--headless")
options.add_argument("--window-size=1920,1080")
options.add_argument("--disable-gpu")
options.add_argument("--no-sandbox")
options.add_argument("--disable-dev-shm-usage")

# Initialize the driver
driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

# List of URLs to process
urls = [
    "https://api.fiftytwo.com/docs/masterdata/",
    "https://api.fiftytwo.com/docs/receipt/",
    "https://api.fiftytwo.com/docs/basket/",
    "https://api.fiftytwo.com/docs/order/",
    "https://api.fiftytwo.com/docs/auth/"
]

# Function to expand all sections thoroughly
def expand_everything():
    print("Starting comprehensive expansion of all content...")
    
    # 1. Click main "Expand all" button
    try:
        expand_all_button = WebDriverWait(driver, 30).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Expand all')]"))
        )
        expand_all_button.click()
        print("Clicked 'Expand all' button")
        time.sleep(5)  # Wait for initial expansion
    except Exception as e:
        print(f"Expand all button issue: {e}")
    
    # 2. Click all operation blocks to expand them
    try:
        operations = driver.find_elements(By.XPATH, "//div[contains(@class, 'opblock-summary')]")
                
        for op in operations:
            try:
                driver.execute_script("arguments[0].click();", op)
                time.sleep(0.5)  # Wait between clicks
            except:
                pass
        time.sleep(3)  # Wait for operations to expand
    except Exception as e:
        print(f"Error expanding operation blocks: {e}")
    
    # 3. Click all expand/toggle buttons
    try:
        expand_buttons = driver.find_elements(By.XPATH, "//button[contains(@class, 'expand') or contains(@class, 'toggle') or contains(@class, 'arrow')]")
        
        for button in expand_buttons:
            try:
                driver.execute_script("arguments[0].click();", button)
                time.sleep(0.2)  # Small delay between clicks
            except:
                pass
    except Exception as e:
        print(f"Error clicking expand/toggle buttons: {e}")
    
    # 4. Click all model buttons
    try:
        model_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Model') or contains(@class, 'model')]")
        
        for button in model_buttons:
            try:
                driver.execute_script("arguments[0].click();", button)
                time.sleep(0.2)  # Small delay between clicks
            except:
                pass
    except Exception as e:
        print(f"Error clicking model buttons: {e}")
    
    # 5. Click all schema buttons
    try:
        schema_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Schema') or contains(@class, 'schema')]")
        
        for button in schema_buttons:
            try:
                driver.execute_script("arguments[0].click();", button)
                time.sleep(0.2)  # Small delay between clicks
            except:
                pass
    except Exception as e:
        print(f"Error clicking schema buttons: {e}")
    
    # 6. Execute JavaScript to expand all possible elements
    try:
        driver.execute_script("""
        // Function to click all buttons in the document
        function clickAllButtons() {
            // Get all buttons
            var buttons = document.querySelectorAll('button');
            
            // Click each button that might expand content
            buttons.forEach(function(button) {
                // Skip buttons that might collapse content
                if (!button.textContent.includes('Collapse') && 
                    !button.textContent.includes('Hide') &&
                    !button.classList.contains('collapsed')) {
                    try {
                        button.click();
                    } catch(e) {}
                }
            });
            
            // Get all elements that might be clickable for expansion
            var expandables = document.querySelectorAll('[class*="expand"], [class*="toggle"], [class*="arrow"], [class*="model"], [class*="schema"]');
            expandables.forEach(function(el) {
                try {
                    el.click();
                } catch(e) {}
            });
        }
        
        // Run multiple times to catch elements that appear after earlier expansions
        for(var i = 0; i < 3; i++) {
            clickAllButtons();
        }
        """)
        print("Executed JavaScript to expand all possible elements")
    except Exception as e:
        print(f"Error executing JavaScript expansion: {e}")
    
    # 7. Execute JavaScript to expand JSON examples
    try:
        driver.execute_script("""
        // Find all JSON objects that might be collapsed
        var preElements = document.querySelectorAll('pre');
        preElements.forEach(function(pre) {
            // If this contains JSON with empty objects, try to expand it
            if(pre.textContent.includes('{}') || pre.textContent.includes('[]')) {
                // Look for nearby expand buttons
                var parent = pre.parentElement;
                for(var i = 0; i < 5; i++) {
                    if(!parent) break;
                    var buttons = parent.querySelectorAll('button');
                    buttons.forEach(function(button) {
                        try {
                            button.click();
                        } catch(e) {}
                    });
                    parent = parent.parentElement;
                }
            }
        });
        """)
        print("Executed JavaScript to expand collapsed JSON")
    except Exception as e:
        print(f"Error executing JavaScript to expand JSON: {e}")
    
    # Wait for all expansions to complete
    print("Waiting for all expansions to complete...")
    time.sleep(5)

# Process each URL
for url_index, url in enumerate(urls):
    try:
        # Extract API name from URL
        api_name = url.split("/")[-2] if url.endswith("/") else url.split("/")[-1]
        print(f"\n[{url_index+1}/{len(urls)}] Processing {api_name} API: {url}")
        
        # Navigate to the URL
        driver.get(url)
        print(f"Opened URL: {url}")
        
        # Wait for page to load
        time.sleep(5)
        
        # Expand everything on the page
        expand_everything()
        
        # Extract the complete page content
        html_content = driver.page_source
        
        # Parse HTML and extract text
        soup = BeautifulSoup(html_content, "html.parser")
        text_content = soup.get_text(separator="\n", strip=True)
        
        # Save to text file
        text_filename = f"{api_name}_api_documentation.txt"
        text_path = os.path.join(output_dir, text_filename)
        
        with open(text_path, "w", encoding="utf-8") as f:
            f.write(text_content)
        
        print(f"Saved {api_name} API documentation to: {text_path}")
        
    except Exception as e:
        print(f"Error processing {url}: {e}")
        import traceback
        traceback.print_exc()
        

# Clean up
driver.quit()
print("\nAll URLs processed. Documentation saved to text files in the output directory.")